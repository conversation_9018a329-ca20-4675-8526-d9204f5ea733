{"name": "cpvf", "version": "2.6.0", "author": {"name": "CPVF", "type": "主应用", "version": "2.6.0"}, "scripts": {"bootstrap": "pnpm install", "dev": "vite", "test": "vitest", "test:ui": "vitest --ui", "test:report": "vitest --coverage", "build": "cross-env REPORT=true vite build --mode production", "build-micro": "cross-env REPORT=true vite build --mode micro", "staging": "cross-env REPORT=true  vite build --mode staging", "serve": "vite preview --host", "type:check": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "prepare": "husky install", "lint": "eslint --fix", "lint:lint-staged": "lint-staged", "stylelint": "stylelint **/*.{html,vue,css,sass,scss,less}"}, "engines": {"node": ">=16"}, "dependencies": {"@ctrl/tinycolor": "3.6.1", "@element-plus/icons-vue": "2.3.1", "@iconify-json/carbon": "^1.2.10", "@iconify/vue": "^4.1.1", "@interactjs/actions": "^1.10.27", "@interactjs/auto-scroll": "^1.10.27", "@interactjs/auto-start": "^1.10.27", "@interactjs/core": "^1.10.27", "@interactjs/dev-tools": "^1.10.27", "@interactjs/interact": "^1.10.27", "@interactjs/modifiers": "^1.10.27", "@monaco-editor/loader": "^1.5.0", "@visactor/vtable": "^1.19.1", "@vueuse/core": "11.0.3", "@vueuse/motion": "2.2.6", "@wangeditor/editor-for-vue": "^5.1.12", "autoprefixer": "10.4.20", "axios": "1.6.0", "crypto-js": "4.1.1", "css-color-function": "1.3.3", "current-device": "0.10.2", "dayjs": "1.11.13", "defu": "6.1.4", "echarts": "^5.6.0", "element-plus": "2.9.10", "element-resize-detector": "1.2.4", "highlight.js": "11.11.1", "interact": "^0.0.3", "interactjs": "^1.10.27", "jquery": "3.7.0", "json-editor-vue": "0.14.0", "localforage": "1.10.0", "lodash": "4.17.21", "lodash-es": "4.17.21", "lodash-unified": "1.0.3", "marked": "^16.0.0", "marked-highlight": "2.2.2", "mitt": "3.0.1", "mockjs": "1.1.0", "monaco-editor": "^0.52.2", "normalize.css": "8.0.1", "nprogress": "0.2.0", "pinia": "2.2.2", "postcss": "8.4.45", "postcss-import": "16.1.0", "prismjs": "^1.30.0", "qrcode.vue": "3.4.1", "qs": "6.13.0", "resize-observer-polyfill": "1.5.1", "responsive-storage": "2.2.0", "rgb-hex": "4.1.0", "screenfull": "6.0.2", "sm-crypto": "^0.3.13", "sortablejs": "1.15.3", "splitpanes": "3.1.5", "store": "2.0.12", "ua-parser-js": "1.0.39", "vite-plugin-html": "3.2.2", "vue": "3.5.5", "vue-dompurify-html": "^5.2.0", "vue-i18n": "10.0.1", "vue-router": "4.4.5", "vue3-slide-verify": "1.1.5", "vuedraggable": "4.1.0", "wujie-vue3": "1.0.6"}, "devDependencies": {"@commitlint/cli": "19.5.0", "@commitlint/config-conventional": "19.5.0", "@testing-library/vue": "7.0.0", "@types/element-resize-detector": "1.1.6", "@types/lodash-es": "4.17.12", "@types/node": "22.5.5", "@types/nprogress": "0.2.0", "@types/qs": "6.9.7", "@types/sortablejs": "1.15.1", "@types/store": "2.0.2", "@types/ua-parser-js": "0.7.36", "@typescript-eslint/eslint-plugin": "8.5.0", "@typescript-eslint/parser": "8.5.0", "@vitejs/plugin-vue": "5.1.3", "@vitejs/plugin-vue-jsx": "4.0.1", "@vitest/coverage-v8": "2.1.1", "@vitest/ui": "2.1.1", "@vue/compiler-sfc": "3.5.5", "@vue/test-utils": "2.4.6", "@wsfe/vue-tree": "^4.1.1", "commitizen": "4.2.3", "cross-env": "7.0.3", "cz-conventional-changelog": "3.3.0", "cz-customizable": "6.3.0", "electron-to-chromium": "1.5.22", "eslint": "8.57.0", "eslint-config-airbnb-base": "15.0.0", "eslint-config-prettier": "8.3.0", "eslint-plugin-import": "2.30.0", "eslint-plugin-prettier": "4.0.0", "eslint-plugin-vue": "7.17.0", "happy-dom": "8.9.0", "husky": "6.0.0", "less": "^4.3.0", "lint-staged": "10.5.4", "postcss-html": "1.7.0", "prettier": "2.3.2", "rollup-plugin-visualizer": "5.12.0", "sass": "1.70.0", "stylelint": "14.16.1", "stylelint-config-html": "1.0.0", "stylelint-config-prettier": "9.0.5", "stylelint-config-recommended": "6.0.0", "stylelint-config-standard": "24.0.0", "stylelint-order": "^5.0.0", "typescript": "5.6.2", "unplugin-auto-import": "0.18.3", "unplugin-vue-components": "0.27.4", "vitawind": "2.3.0", "vite": "4.5.5", "vite-plugin-compression": "0.5.1", "vite-plugin-mock": "3.0.2", "vite-plugin-pages": "0.32.3", "vite-plugin-svg-icons": "2.0.1", "vite-plugin-vue-layouts": "0.11.0", "vite-plugin-vue-setup-extend": "0.4.0", "vite-plugin-windicss": "1.9.3", "vitest": "2.1.1", "vue-global-api": "0.4.1", "vue-tsc": "2.1.6", "windicss": "3.5.6"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "{!(package)*.json,*.code-snippets,.!(browserslist|npm|lintstaged)*rc}": ["prettier --write--parser json"], "package.json": ["prettier --write"], "*.vue": ["eslint --fix", "prettier --write", "stylelint --fix"], "*.{scss,less,styl,html}": ["stylelint --fix", "prettier --write"], "*.md": ["prettier --write"]}}