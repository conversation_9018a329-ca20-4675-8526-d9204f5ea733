import { defHttp } from '/@/utils/axios/index';

enum Api {
  auditLogs = '/sys/system/sysOperLog/pageAuditLogs',
  oprLogs = '/sys/system/sysOperLog/pageByParams',
}

// export const getAuditLogsApi = () => {
//   return defHttp.get({ url: Api.auditLogs });
// };

export const getAuditLogsApi = (params?: any) => {
  return defHttp.post({
    url: Api.auditLogs,
    params,
  });
};

export const getOprLogsApi = (params) => {
  return defHttp.post({
    url: Api.oprLogs,
    params,
  });
};
