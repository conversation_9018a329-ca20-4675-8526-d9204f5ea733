<template>
  <div class="role-container">
    <div class="role-header">
      <h3>团队角色管理</h3>
    </div>

    <!-- 搜索区域 -->
    <div class="search-section">
      <el-form :inline="true" :model="searchParams" class="demo-form-inline">
        <el-form-item label="">
          <el-input
            v-model="searchParams.name"
            placeholder="请输入角色名称"
            clearable
          />
        </el-form-item>
        <el-form-item label="">
          <el-input
            v-model="searchParams.description"
            placeholder="请输入角色描述"
            clearable
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 操作按钮区域 -->
    <div class="action-section">
      <el-button type="primary" @click="handleAdd" :icon="Plus"> 新增角色 </el-button>
      <el-button
        type="danger"
        @click="handleBatchDelete"
        :icon="Delete"
        :disabled="selectedIds.length === 0"
      >
        批量删除
      </el-button>
      <el-button @click="handleRefresh" :icon="Refresh"> 刷新 </el-button>
    </div>

    <!-- 表格区域 -->
    <div class="table-section">
      <el-table
        ref="tableRef"
        :data="tableData"
        v-loading="loading"
        style="width: 100%"
        stripe
        @selection-change="handleSelectionChange"
        border
        height="calc(100vh - 410px)"
      >
        <el-table-column type="selection" width="40" />
        <el-table-column type="index" label="序号" width="60" align="center" />
        <el-table-column prop="name" label="角色名称" show-overflow-tooltip />
        <el-table-column prop="description" label="角色描述" show-overflow-tooltip />
        <el-table-column
          prop="sort"
          width="90"
          label="显示顺序"
          show-overflow-tooltip
          align="center"
        />
        <el-table-column label="操作" fixed="right" width="240" align="center">
          <template #default="{ row }">
            <el-button type="primary" link size="small" @click="handleAssignUsers(row)">
              分配用户
            </el-button>
            <el-button type="primary" link size="small" @click="handlePermission(row)">
              数据权限
            </el-button>
            <el-button type="primary" link size="small" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button type="danger" link size="small" @click="handleDelete(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </div>

    <!-- 角色编辑对话框 -->
    <el-drawer
      v-model="dialogVisible"
      :title="dialogTitle"
      size="600px"
      :close-on-click-modal="false"
      destroy-on-close
    >
      <el-form ref="formRef" :model="formData" :rules="formRules" label-width="100px">
        <el-form-item label="角色名称" prop="name">
          <el-input v-model="formData.name" placeholder="请输入角色名称" />
        </el-form-item>
        <el-form-item label="角色描述" prop="description">
          <el-input v-model="formData.description" placeholder="请输入角色描述" />
        </el-form-item>
        <el-form-item label="显示顺序" prop="sort">
          <el-input-number v-model="formData.sort" :min="0" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
          确定
        </el-button>
      </template>
    </el-drawer>

    <!-- 分配用户对话框 -->
    <el-dialog
      v-model="userDialogVisible"
      title="分配用户"
      width="700px"
      :close-on-click-modal="false"
      destroy-on-close
    >
      <el-transfer
        v-model="selectedUserIds"
        :data="userOptions"
        :titles="['未授权用户', '已授权用户']"
        :button-texts="['取消', '添加']"
        :props="{ key: 'id', label: 'name' }"
        filterable
        filter-placeholder="请输入姓名"
        style="text-align: left; display: inline-block"
      />
      <template #footer>
        <el-button @click="userDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmitUsers" :loading="submitLoading">
          确定
        </el-button>
      </template>
    </el-dialog>

    <el-drawer
      v-model="drawerVisible"
      title="数据权限"
      size="1200px"
      :close-on-click-modal="false"
      destroy-on-close
    >
      <div>
        <el-form>
          <el-form-item label="角色名称" prop="name">
            <!-- <el-input v-model="formData.name" placeholder="请输入角色名称" /> -->
            <span>{{ localInfoData.name }}</span>
          </el-form-item>
        </el-form>
        <!-- localInfoData.value -->
        <el-table :data="dataPermissionList" style="width: 100%" stripe border>
          <el-table-column
            prop="datasourceLabel"
            width="280"
            label="数据来源"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <el-tag>{{ row.datasourceLabel }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column
            prop="tableName"
            width="180"
            label="表名称"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <el-tag>{{ row.tableName }}</el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="permissions" label="数据权限" align="left">
            <template #default="{ row, $index }">
              <el-checkbox-group
                v-model="row.permissionValue"
                @change="handleChange(row.permissionValue, $index)"
              >
                <el-checkbox
                  v-for="item in row.permissions"
                  :value="item.id"
                  :key="item.id"
                  border
                  >{{ item.name }}</el-checkbox
                >
              </el-checkbox-group>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <template #footer>
        <el-button @click="drawerVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSavePermission"> 确定 </el-button>
      </template>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
    import { ref, reactive, onMounted } from 'vue';
    import { ElMessage, ElMessageBox } from 'element-plus';
    import { Plus, Refresh, Delete } from '@element-plus/icons-vue';
    import {
      getRolePageList,
      saveRole,
      updateRole,
      deleteRoles,
      getRoleDetail,
      getAuthRoleDetail,
      saveAuthRole,
      getAllPermissionApi,
      roleDataPermissionApi,
      getPermissionByIdApi,
      type RoleInfo,
      type RoleQueryParams
    } from '/@/api/sys/roles';
   import { getTeamInfoApi } from '../../api/index';
    // import {
    //   getDataPermissionList,
    // } from '/@/views/team/api/index'

    import {
      getDatasourceApi,
    } from '/@/views/datasource/api/index'

  // getDatasourceApi
    // 响应式数据
    const tableRef = ref();
    const formRef = ref();
    const menuTreeRef = ref();

    const tableData = ref<RoleInfo[]>([]);
    const loading = ref(false);
    const submitLoading = ref(false);
    const selectedIds = ref<string[]>([]);


    // 分配用户相关
    const userDialogVisible = ref(false);
    const selectedUserIds = ref<string[]>([]);
    const userOptions = ref<any[]>([]);

    const currentRoleId = ref<string>('');

    // 分页数据
    const pagination = reactive({
      current: 1,
      pageSize: 10,
      total: 0,
    });

    // 对话框状态
    const dialogVisible = ref(false);
    const dialogTitle = ref('');
    const isEdit = ref(false);

    const drawerVisible = ref(false)


    // 表单数据
    const formData = ref<RoleInfo>({
      name: '',
      description: '',
      sort: 0,
      status: '0',
      remark: '',
    });
    const teamId = ref('');

    const localInfoData = ref<any>({});

    const dataPermissionList = ref<any[]>([]);

    // 表单验证规则
    const formRules = {
      name: [
        { required: true, message: '请输入角色名称', trigger: 'blur' }
      ],
      description: [
        { required: true, message: '请输入角色描述', trigger: 'blur' }
      ],
      status: [
        { required: true, message: '请选择状态', trigger: 'change' }
      ]
    };

    // 搜索参数
    const searchParams = ref<RoleQueryParams>({});

    // 表格选择变化
    const handleSelectionChange = (selection: RoleInfo[]) => {
      selectedIds.value = selection.map(item => item.id!);
    };

    const findNameById = (array, id) => {
      // 方法1: 使用find方法 (ES6+)
      const item = array.find(obj => obj.id == id);
      return item ? item.alias : null;
    }
    const getAllPermission = async (datasourceOptions) => {
       const res = await getAllPermissionApi();
       console.log(res);
       dataPermissionList.value = res;
       for (let i = 0; i<dataPermissionList.value.length; i++) {
          dataPermissionList.value[i].permissionValue = [];
          dataPermissionList.value[i].selectedValue = null;
          dataPermissionList.value[i].datasourceLabel = findNameById(datasourceOptions, dataPermissionList.value[i].datasourceId) + '/' + dataPermissionList.value[i].databaseName + '/' + dataPermissionList.value[i].schemaName;
       }
       console.log('getAllPermission');
       console.log(dataPermissionList.value);
    }

    const getDatasource = async () => {
      //
      const res = await getDatasourceApi();
      console.log('getDatasourceApi');
      // console.log(datasourceOptions);
      getAllPermission(res.data)
    };

    const handleChange = (val, index) => {
      console.log(val);
      console.log(index);

      if (val.length>1) {
        dataPermissionList.value[index].permissionValue = [val[val.length - 1]]
      }
    }

    const handleSavePermission = () => {

      if (!!teamId.value) {
        let permissionIds:any = [];

        for (let i = 0; i < dataPermissionList.value.length;i++) {
          for (let j = 0; j < dataPermissionList.value[i].permissionValue.length; j++) {
              permissionIds.push(dataPermissionList.value[i].permissionValue[j])
          }
        }
        const params = {
          roleId: localInfoData.value.id,
          permissionIds,
          teamId: teamId.value
        }

        console.log(params);
        roleDataPermissionApi(params).then((res) => {

          drawerVisible.value = false;
          console.log(res)
          ElMessage.success('绑定成功');
        })
      } else {
        ElMessage.info('请先在团队信息中添加团队');
      }




    //  roleDataPermissionApi
    }


    const handlePermission = async (record) => {
      console.log(record);
      drawerVisible.value = true;
      localInfoData.value = record;

      const res: string[] = await getPermissionByIdApi(localInfoData.value.id);
      console.log(res);
      for (let i = 0; i < dataPermissionList.value.length;i++) {
          // for (let j = 0; j < dataPermissionList.value[i].permissionValue.length; j++) {
          //       permissionIds.push(dataPermissionList.value[i].permissionValue[j])
          //   }
          // }
          // dataPermissionList.value[i].permissionValue = res;
          for (let j = 0; j < dataPermissionList.value[i].permissions.length; j++) {
            //  if (dataPermissionList.value[i].permissions.id) {

            //  }

            if (res.length > 0) {
              const foundItem = res.find(item => item ===  dataPermissionList.value[i].permissions[j].id);
              if (foundItem) {
                dataPermissionList.value[i].permissionValue = [foundItem];
              };
            } else {
              dataPermissionList.value[i].permissionValue = []
            }
          }

      }
    }
    const getTeamInfo = async () => {
      const res = await getTeamInfoApi();
      // console.log(res);
      // if (res.length > 0) {
      //   teamId.value = res[0].id
      // } else {
      //   teamId.value = ''
      // }

      teamId.value = res.currentTeam.id
    };

    // const handleRadioClick = (val, index) => {
    //   console.log(dataPermissionList.value[index]);
    //   console.log(val);

    //   if (dataPermissionList.value[index].permissionValue === val.id) {
    //     // dataPermissionList.value[index].selectedValue = null;
    //     dataPermissionList.value[index].permissionValue = null;
    //     console.log(dataPermissionList.value[index]);
    //   } else {
    //     dataPermissionList.value[index].permissionValue = val.id;
    //   }
    // }



    // 生命周期
    onMounted(() => {
      loadRoleList();
      getDatasource();
      getTeamInfo();
      // getAllPermission()
    });

    // 加载角色列表
    const loadRoleList = async () => {
      try {
        loading.value = true;
        const params = {
          ...searchParams.value,
          currentPage: pagination.current,
          pageSize: pagination.pageSize,
          roleType: 'data'
        };
        console.log(params);
        const result = await getRolePageList(params);
        if (result && result.data) {
          tableData.value = result.data;
          pagination.total = result.total;
        }
      } catch (error) {
        ElMessage.error('加载角色列表失败');
        console.error('Load role list error:', error);
      } finally {
        loading.value = false;
      }
    };

    // 搜索处理
    const handleSearch = () => {
      pagination.current = 1;
      loadRoleList();
    };

    // 重置搜索
    const handleReset = () => {
      searchParams.value = {};
      pagination.current = 1;
      loadRoleList();
    };

    // 刷新
    const handleRefresh = () => {
      loadRoleList();
    };

    // 分页处理
    const handlePageChange = (page: number) => {
      pagination.current = page;
      loadRoleList();
    };

    const handleSizeChange = (size: number) => {
      pagination.pageSize = size;
      pagination.current = 1;
      loadRoleList();
    };


    // 新增角色
    const handleAdd = async () => {
      dialogTitle.value = '新增角色';
      isEdit.value = false;
      formData.value = {
        name: '',
        description: '',
        sort: 0,
        status: '0',
        remark: '',
      };
      // await initMenuTree();
      dialogVisible.value = true;
    };

    // 编辑角色
    const handleEdit = async (record: RoleInfo) => {
      dialogTitle.value = '编辑角色';
      isEdit.value = true;
      formData.value = { ...record };
      console.log('编辑角色数据:', record);
      const res = await getRoleDetail(record.id);
      console.log(res);
      dialogVisible.value = true;
    };

    // 提交表单
    const handleSubmit = async () => {
      try {
        const valid = await formRef.value?.validate();
        if (!valid) return;

        submitLoading.value = true;

        // 获取选中的菜单权限
        const checkedKeys = menuTreeRef.value?.getCheckedKeys() || [];
        const halfCheckedKeys = menuTreeRef.value?.getHalfCheckedKeys() || [];
        const menusIds = [...checkedKeys, ...halfCheckedKeys];

        const roleData = {
          ...formData.value,
          menusIds,
          roleType: 'data'
        };

        if (isEdit.value) {
          await updateRole(roleData);
          ElMessage.success('更新角色成功');
        } else {
          await saveRole(roleData);
          ElMessage.success('新增角色成功');
        }

        dialogVisible.value = false;
        loadRoleList();
      } catch (error) {
        ElMessage.error(isEdit.value ? '更新角色失败' : '新增角色失败');
        console.error('Submit role error:', error);
      } finally {
        submitLoading.value = false;
      }
    };

    // 删除角色
    const handleDelete = async (record: RoleInfo) => {
      await ElMessageBox.confirm('确定要删除该角色吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      });

      await deleteRoles(record.id!);
      ElMessage.success('删除角色成功');
      loadRoleList();
    };

    // 批量删除角色
    const handleBatchDelete = async () => {
      try {
        await ElMessageBox.confirm(`确定要删除选中的 ${selectedIds.value.length} 个角色吗？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        });

        await deleteRoles(selectedIds.value.join(','));
        ElMessage.success('批量删除角色成功');
        selectedIds.value = [];
        loadRoleList();
      } catch (error) {
        if (error !== 'cancel') {
          ElMessage.error('批量删除角色失败');
          console.error('Batch delete roles error:', error);
        }
      }
    };

    // 分配用户
    const handleAssignUsers = async (record) => {
      currentRoleId.value = record.id;
      const res = await getAuthRoleDetail(record.id)
      selectedUserIds.value = res?.hasUsersIds;
      userDialogVisible.value = true;
      userOptions.value = res?.usersAll;
    };



    // 提交用户分配
    const handleSubmitUsers =  () => {
      saveAuthRole(currentRoleId.value, selectedUserIds.value).then(() => {
        ElMessage.success('分配用户成功');
        userDialogVisible.value = false;
      }).catch(() => {
        ElMessage.error('分配用户失败');
      })

      // try {
      //   submitLoading.value = true;
      //   // await assignRoleUsers(currentRoleId.value, selectedUserIds.value);

      //   ElMessage.success('分配用户成功');
      //   userDialogVisible.value = false;
      // } catch (error) {
      //   ElMessage.error('分配用户失败');
      //   console.error('Assign users error:', error);
      // } finally {
      //   submitLoading.value = false;
      // }
    };
</script>

<style scoped lang="scss">
  .role-container {
    padding: 24px;
    background-color: #fff;
    border-radius: 8px;
    margin: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .role-header {
      margin-bottom: 24px;
      padding-bottom: 12px;
      border-bottom: 2px solid #f0f2f5;

      h3 {
        margin: 0;
        font-size: 20px;
        font-weight: 600;
        color: #1f2937;
      }
    }

    .search-section {
      margin-bottom: 20px;
      // padding: 20px;
      // background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
      // border-radius: 8px;
      // border: 1px solid #e2e8f0;
    }

    .action-section {
      margin-bottom: 20px;
      display: flex;
      gap: 12px;
      // align-items: center;
      justify-content: right;
    }

    .table-section {
      background: #fff;
      border-radius: 8px;
      overflow: hidden;

      .pagination-section {
        margin-top: 20px;
        padding: 16px 0;
        display: flex;
        justify-content: flex-end;
        // background: #fafbfc;
        // border-top: 1px solid #e2e8f0;
      }
    }
  }

  :deep(.el-dialog__body) {
    padding: 20px;
  }
</style>
