import { defHttp } from '/@/utils/axios';

// 角色管理相关接口
enum Api {
  RolePageList = '/sys/system/sysRoles/pageByParams',
  RoleList = '/sys/system/sysRoles/list',
  RoleDetail = '/sys/system/sysRoles/get/{id}',
  RoleSave = '/sys/system/sysRoles/save',
  RoleUpdate = '/sys/system/sysRoles/updateById',
  RoleDelete = '/sys/system/sysRoles/delete/{ids}',
  AppRoleUpdate = '/sys/system/sysRoles/updateAppById',
  AuthRole = '/sys/system/user/getAuthRole/{id}',
  SaveAuthRole = '/sys/system/user/saveAuthRole/',
  AllPermission = '/sys/system/dataPermission/groupedList',
  roleDataPermission = '/sys/system/roleDataPermission/batchUpdate',

  getPermissionById = '/sys/system/roleDataPermission/listByRoleId/',
}

// 角色查询参数
export interface RoleQueryParams {
  name?: string;
  description?: string;
  status?: string;
  currentPage?: number;
  pageSize?: number;
}

// 角色信息
export interface RoleInfo {
  id?: string;
  name: string;
  description: string;
  sort?: number;
  status?: string; // 0正常 1停用
  delFlag?: string;
  remark?: string;
  createTime?: string;
  updateTime?: string;
  menusIds?: string[];
}

// 角色分页结果
export interface RolePageResult {
  currentPage: number;
  pageSize: number;
  total: number;
  data: RoleInfo[];
}

/**
 * 分页查询角色列表
 */
export const getRolePageList = (params: RoleQueryParams) =>
  defHttp.post<RolePageResult>({
    url: `${Api.RolePageList}?currentPage=${params.currentPage || 1}&pageSize=${
      params.pageSize || 10
    }`,
    params: {
      name: params.name,
      description: params.description,
      status: params.status,
      roleType: params.roleType,
    },
  });

export const getAllPermissionApi = () => defHttp.get({ url: Api.AllPermission });

export const roleDataPermissionApi = (params?: any) =>
  defHttp.post<RoleInfo[]>({ url: Api.roleDataPermission, params });

// getPermissionById
export const getPermissionByIdApi = (id: string) =>
  defHttp.get({
    url: Api.getPermissionById + id,
  });
/**
 * 获取角色详情
 */
export const getRoleDetail = (id: string) =>
  defHttp.get<RoleInfo>({ url: Api.RoleDetail.replace('{id}', id) });

export const getAuthRoleDetail = (id: string) =>
  defHttp.get({ url: Api.AuthRole.replace('{id}', id) });

/**
 * 查询所有角色列表
 */
export const getAllRoles = (params?: any) =>
  defHttp.post<RoleInfo[]>({ url: Api.RoleList, params });

export const saveAuthRole = (roleId, params?: any) =>
  defHttp.post({ url: Api.SaveAuthRole + roleId, params });
// SaveAuthRole

/**
 * 根据ID查询角色详情
 */
export const getRoleById = (id: string) =>
  defHttp.get<RoleInfo>({ url: Api.RoleDetail.replace('{id}', id) });

/**
 * 新增角色
 */
export const saveRole = (params: RoleInfo) =>
  defHttp.post({ url: Api.RoleSave, params });

/**
 * 更新角色
 */
export const updateRole = (params: RoleInfo) =>
  defHttp.post({ url: Api.RoleUpdate, params });

/**
 * 更新APP角色
 */
export const updateAppRole = (params: RoleInfo) =>
  defHttp.post({ url: Api.AppRoleUpdate, params });

/**
 * 删除角色
 */
export const deleteRoles = (ids: string) =>
  defHttp.get({ url: Api.RoleDelete.replace('{ids}', ids) });

/**
 * 获取角色菜单权限
 */
export const getRoleMenus = (roleId: string) =>
  defHttp.get<any[]>({ url: `/system/sysRoles/menus/${roleId}` });

/**
 * 保存角色菜单权限
 */
export const saveRoleMenus = (params: { roleId: string; menusIds: string[] }) =>
  defHttp.post({ url: '/system/sysRoles/saveMenus', params });
