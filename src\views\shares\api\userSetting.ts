import { updatePwdParams } from '/@/views/shares/api/model/userSetting';
import { sharesHttp } from '/@/views/shares/common/http';

enum Api {
  UpdatePassword = '/system/user/updatePassword',
  info = '/system/sysUsers/updateInfo',
}
// 修改账号密码
export const UpdatePassword = (params: updatePwdParams) =>
  sharesHttp.post({ url: Api.UpdatePassword, params });
// 修改用户信息
export const updateInfo = (params) => sharesHttp.post({ url: Api.info, params });
