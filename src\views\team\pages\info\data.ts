import { FormOptions } from '/@/components/sys/BasicForm/types';
export const formSchema: FormOptions[] = [
  {
    field: 'name',
    label: '团队名称',
    component: 'Input',
    componentProps: {
      placeholder: '请输入团队名称',
    },
    required: true,
  },
  {
    field: 'description',
    label: '团队描述',
    component: 'InputTextArea',
    componentProps: {
      placeholder: '请输入团队描述',
      rows: 3,
    },
    // required: true,
  },
];
