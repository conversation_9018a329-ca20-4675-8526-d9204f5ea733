export const vtableTheme = {
      underlayBackgroundColor: '#FFF',
      // selectionBgColor: '#000',
      defaultStyle: {
        color: '#1B1F23',
        bgColor: '#fbfbfc',
        fontSize: 14,
        fontFamily: 'PingFang SC',
        fontWeight: 600,
        lineHeight: 14,
        borderColor: '#fbfbfc',
        padding: [8, 12, 8, 12]
      },
      headerStyle: {
        color: '#1B1F23',
        bgColor: 'rgba(0, 0, 0, 0.05)',
        // 优先使用 Arial 字体，等宽能够保证数字场景长度一致，英文加粗场景 Arial 没有 500 字重，所以使用 600
        fontSize: 14,
        fontWeight: 600,
        lineHeight: 14,
        borderColor: '#fbfbfc',
        padding: [8, 12, 8, 12],
        hover: {
          cellBgColor: '#EEF1F5'
        },
      },
      rowHeaderStyle: {
        color: '#1B1F23',
        bgColor: '#EEF1F5',
        fontSize: 14,
        fontFamily: 'PingFang SC',
        fontWeight: 600,
        lineHeight: 16,
        borderColor: '#EEF1F5',
        padding: [8, 12, 8, 12],
        hover: {
          cellBgColor: '#c8daf6'
        },
      },
      bodyStyle: {
        padding: [8, 12, 8, 12],
        color: '#141414',
        fontSize: 14,
        fontFamily: 'Arial,sans-serif',
        fontWeight: 400,
        textAlign: 'left',
        bgColor: '#fbfbfc',
        borderColor: '#EEF1F5',
        lineHeight: 14,
        hover: {
          cellBgColor: '#EEF1F5'
        }
      },
      columnResize: {
        lineWidth: 1,
        lineColor: '#416EFF',
        bgColor: '#D9E2FF',
        width: 3
      },
      selectionStyle: {
        cellBgColor: 'rgba(0, 0, 0, 0.05)',
        inlineRowBgColor: '#EEF1F5',
        cellBorderLineWidth: 1,
        cellBorderColor: '#EEF1F5',
        bgColor: '#EEF1F5',
      },
      tooltipStyle: {
        bgColor: '#FFF',
        color: '#000',
        fontSize: 12,
        fontFamily: 'Arial,sans-serif'
      }
    }