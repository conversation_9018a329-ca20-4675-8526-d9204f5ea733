<template>
  <div class="base-table-all">
    <div
      v-show="setting"
      :class="['flex', 'justify-end', 'setting', { 'down-setting': downSetting }]"
    >
      <el-popover placement="top-start" :width="200" trigger="click">
        <template #reference>
          <Setting @click="handleSelection" style="width: 1.5em; height: 1.5em" />
        </template>
        <el-table
          class="sort-table"
          ref="multipleTableRef"
          row-key="prop"
          :data="allColumns"
          style="width: 100%; height: 150px"
          size="small"
          :key="itemKey"
          @selection-change="handleSelectionChange"
        >
          <el-table-column width="28">
            <el-icon title="拖拽" class="cursor-pointer">
              <rank />
            </el-icon>
          </el-table-column>
          <el-table-column type="selection" width="30" />
          <el-table-column property="label" label="字段名" />
        </el-table>
      </el-popover>
      <div style="width: 1.5em; height: 1.5em; margin-right: 8px"></div>
    </div>
    <el-table
      ref="dataTableRef"
      v-bind="$attrs"
      class="table-data"
      :data="data"
      :row-key="rowKey"
      :key="itemKey"
      v-loading="loading"
      :height="height"
      stripe
      border
      @selection-change="handleSelectionChangeTableData"
      @current-change="handleCurrentRowSelectChange"
      @sort-change="handleSortChange"
    >
      <template v-for="(item, index) in useColumns" :key="index + item.label">
        <el-table-column
          v-if="item.type"
          :type="item.type"
          :width="item.width"
          :align="item.align"
          :selectable="item.selectable"
          :reserve-selection="item.type === 'selection' ? true : false"
        />
        <template v-else>
          <el-table-column
            v-if="
              (typeof item.hideColumn === 'function'
                ? !item.hideColumn()
                : !item.hideColumn) && !item.action
            "
            :label="item.label"
            :prop="item.prop"
            :width="item.width"
            :min-width="item.minWidth"
            :align="item.align"
            :sortable="item.sortable"
            :show-overflow-tooltip="!!item.tooltip ? item.tooltip : true"
            :header-align="!!item.headerAlign ? item.headerAlign : 'center'"
          >
            <template v-if="item.headerSlot" #header="scope">
              <slot
                :name="item.headerSlot"
                :record="scope.row"
                :column="item.prop"
              ></slot>
            </template>
            <template v-if="item.slot" #default="scope">
              <slot
                :name="item.slot"
                :record="scope.row"
                :column="item.prop"
                :index="index"
              ></slot>
            </template>
            <template v-else-if="item.ellipsis" #default="scope">
              <span class="column-ellipsis" :title="scope.row[item.prop]">{{
                scope.row[item.prop]
              }}</span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="
              (typeof item.hideColumn === 'function'
                ? !item.hideColumn()
                : !item.hideColumn) && item.action
            "
            :label="actionColumn?.label"
            :prop="actionColumn?.prop"
            :width="actionColumn?.width"
            :align="actionColumn?.align"
            :fixed="actionColumn?.fixed"
          >
            <template #default="scope">
              <slot name="action" :record="scope.row" :index="scope.$index"></slot>
            </template>
          </el-table-column>
        </template>
      </template>
    </el-table>
    <div class="py-5 flex justify-end" v-if="pagination">
      <el-config-provider :locale="locale">
        <el-pagination
          v-bind="$attrs"
          v-model:currentPage="currentPageNum"
          v-model:page-size="pageSizeNum"
          :page-sizes="pageSizes"
          :disabled="disabled"
          :background="background"
          :layout="pageLayouts"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </el-config-provider>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import {
    computed,
    ComputedRef,
    PropType,
    onMounted,
    nextTick,
    ref,
    watch,
    onUnmounted,
  } from 'vue';
  import { TableOptions } from '../types';
  import { usePagination } from './usePagination';
  import Sortable from 'sortablejs';
  import { Setting, Rank } from '@element-plus/icons-vue';
  import { cloneDeep } from 'lodash-es';
  import { ElTable } from 'element-plus';
  import { useBasicTable } from '/@/components/sys/BasicTable/src/useBasicTable';
  import { useRouter } from 'vue-router';
  import { setStorage, getStorage } from '/@/utils/storage';
  import { TABLE_LOCAL } from '/@/enums/cacheEnum';

  let props = defineProps({
    columns: {
      type: Array as PropType<TableOptions[]>,
      required: true,
    },
    rowKey: {
      type: String as PropType<string>,
      default: 'id',
    },
    data: {
      type: Array as PropType<any[]>,
      required: true,
    },
    currentPage: {
      type: Number as PropType<number>,
      default: 1,
    },
    pageSize: {
      type: Number as PropType<number>,
      default: 10,
    },
    pageSizes: {
      type: Array as PropType<number[]>,
      default: () => [10, 20, 30, 40, 50, 100],
    },
    pageLayouts: {
      type: String as PropType<string>,
      default: 'total, sizes, prev, pager, next, jumper',
    },
    total: {
      type: Number as PropType<number>,
      default: 0,
    },
    loading: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
    // 分页是否显示，默认显示
    pagination: {
      type: Boolean as PropType<boolean>,
      default: true,
    },
    // 是否显示右上角setting
    setting: {
      type: Boolean as PropType<boolean>,
      default: true,
    },
    // 设置按钮放到表头中
    downSetting: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
    // 是否使用缓存列功能；通过右上角的设置，可以控制列的显示与隐藏，
    // 开启缓存后，同一用户的修改会被缓存，退出登录会清除
    // 缓存的key值默认使用当前路由，如果页面中有多个table，可以手动传入缓存的key(localTableKey)
    uselocal: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
    localTableKey: {
      type: String as PropType<string>,
      default: null,
    },
    height: {
      type: String as PropType<string>,
      default: 'auto',
    },
  });
  const itemKey = ref();
  watch(
    () => props.columns,
    () => {
      // 支持动态渲染字段
      itemKey.value = Math.random();
      init();
      allColumns.value = cloneDeep(props.columns);
    },
  );
  const { currentRoute } = useRouter();
  const localKey = props.localTableKey || currentRoute.value.path;
  const multipleTableRef = ref<InstanceType<typeof ElTable>>();
  const dataTableRef = ref<InstanceType<typeof ElTable>>();
  // 所有字段，用作筛选
  const allColumns = ref<TableOptions[]>([]);
  // allColumns.value = cloneDeep(props.columns);
  // 调整后的字段
  const useColumns = ref<TableOptions[]>([]);
  const { initColumns } = useBasicTable(props, localKey);
  function init() {
    useColumns.value = initColumns();
    allColumns.value = cloneDeep(useColumns.value);
  }
  init();
  let emits = defineEmits([
    'sizeChange',
    'pageChange',
    'currentRowSelect',
    'selectionChange',
    'sortChange',
  ]);
  let actionColumn: ComputedRef<TableOptions | undefined> = computed(() =>
    useColumns.value.find((item) => item.action),
  );
  let currentPageNum: ComputedRef<number> = computed(() => props.currentPage);
  let pageSizeNum: ComputedRef<number> = computed(() => props.pageSize);
  const { handleSizeChange, handleCurrentChange, background, disabled, locale } =
    usePagination(emits);
  // 行拖拽
  const sortDrop = () => {
    nextTick(() => {
      const wrapper = document.querySelector(
        '.sort-table .el-table__body-wrapper tbody',
      );
      Sortable.create(wrapper, {
        animation: 300,
        delay: 0,
        onEnd: ({ newIndex, oldIndex }) => {
          const temp = allColumns.value.splice(oldIndex, 1);
          allColumns.value.splice(newIndex, 0, temp[0]);
          const useColumnKey = useColumns.value.map((item) => item.prop);
          useColumns.value = allColumns.value.filter((item) =>
            useColumnKey.includes(item.prop),
          );
        },
      });
    });
  };

  function handleSelection() {
    useColumns.value.forEach((row) => {
      multipleTableRef.value!.toggleRowSelection(
        allColumns.value.find((item) => item.prop === row.prop),
        true,
      );
    });
  }
  function handleSelectionChangeTableData(val) {
    console.log(val);
    // dataTableRef.value!.toggleRowSelection([], undefined);
    emits('selectionChange', val);
  }
  function handleSelectionChange(value) {
    const selectionKey = value.map((item) => item.prop);
    if (selectionKey.length > 0) {
      useColumns.value = allColumns.value.filter((item) =>
        selectionKey.includes(item.prop),
      );
    }
  }

  /**
   * 表格行选中
   */
  function handleCurrentRowSelectChange(currentRow, oldCurrentRow) {
    emits('currentRowSelect', currentRow, oldCurrentRow);
  }

  function setSelectData(rows: any) {
    if (!!rows) {
      rows.forEach((row) => {
        console.log(row);
        dataTableRef.value?.toggleRowSelection(row, true);
      });
    }

    // dataTableRef.value?.setCurrentRow(row);
    // dataTableRef.value!.toggleRowSelection(row, true);
  }

  /**
   * 设置 行选中
   * @param row
   */
  function setCurrentRow(row = null) {
    dataTableRef.value?.setCurrentRow(row);
  }
  /**
   * 清空选中
   * @param row
   */
  function clearSelection() {
    dataTableRef.value?.clearSelection();
  }

  const handleSortChange = ({ column, prop, order }) => {
    console.log(column);
    console.log(prop);
    console.log(order);
    // order => ascending正序  descending倒序
    emits('sortChange', { prop, order });
  };

  onMounted(() => {
    nextTick(() => {
      sortDrop();
    });
  });
  onUnmounted(() => {
    if (props.uselocal) {
      const tableStorageList: string[] = getStorage(TABLE_LOCAL) || [];
      if (tableStorageList.indexOf(localKey) == -1) {
        tableStorageList.push(localKey);
        setStorage(TABLE_LOCAL, Array.from(new Set(tableStorageList)));
      }
      setStorage(localKey, useColumns.value);
    }
  });
  defineExpose({
    handleSizeChange,
    handleCurrentChange,
    setCurrentRow,
    setSelectData,
    clearSelection,
  });
  // 分页
</script>
<style scope>
  .column-ellipsis {
    width: 100%;
    /* display: inline-block; */
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .column-group-item {
    box-sizing: border-box;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: normal;
    word-break: break-all;
    line-height: 23px;
    padding: 0 12px;
  }

  .column-group {
    position: absolute;
    display: flex;
    margin-top: 8px;
    width: 100%;
    z-index: 2;
    cursor: move;
  }

  .base-table-all {
    position: relative;
  }

  .setting {
    padding: 12px 0;
  }

  .down-setting {
    cursor: pointer;
    position: absolute;
    padding: 11px 0;
    right: 0;
    z-index: 3;
  }
</style>

<style>
  .el-table tbody tr:hover > td {
    background: #f0f4ff !important;
  }

  .el-table__body .el-table__row.hover-row td {
    background-color: #f0f4ff !important;
  }
</style>
