import { defHttp } from '/@/utils/axios/index';
import {
  DataPermissionItem,
  RolePermissionItem,
  ApiKeyItem,
} from '/@/views/team/types';

enum Api {
  DataPermissionList = '/system/dataPermission/list',
  DataPermissionCreate = '/system/dataPermission/save',
  DataPermissionUpdate = '/system/dataPermission/update',
  DataPermissionDelete = '/system/dataPermission/delete',
  DataPermissionDetail = '/system/dataPermission/detail',
  RolePermissionList = '/system/rolePermission/list',
  RolePermissionCreate = '/system/rolePermission/create',
  RolePermissionUpdate = '/system/rolePermission/update',
  RolePermissionDelete = '/system/rolePermission/delete',
  RolePermissionDetail = '/system/rolePermission/detail',
  executeSql = '/api/rdb/dml/execute',
  ApiKeyList = '/system/apiKey/list',
  ApiKeyCreate = '/system/apiKey/create',
  ApiKeyUpdate = '/system/apiKey/update',
  ApiKeyDelete = '/system/apiKey/delete',
  ApiKeyDetail = '/system/apiKey/detail',
  teamInfo = '/sys/system/team/currentUserTeams',
  temamEdit = '/sys/system/team/update',
  temamAdd = '/sys/system/team/save',

  teamMember = '/sys/system/team/members',
  teamAddUser = '/sys/system/team/addUser',
  teamUpdateMember = '/sys/system/team/updateMember',
  teamRemoveMember = '/sys/system/team/removeMember',
  switch = '/system/team/switch',
  deleteTeam = '/system/team/delete',
}

// 获取数据权限列表
export const getDataPermissionList = (params?: any) => {
  return defHttp.get({ url: Api.DataPermissionList, params });
};

// 创建数据权限
export const createDataPermission = (data: DataPermissionItem) => {
  return defHttp.post({ url: Api.DataPermissionCreate, data });
};

// 更新数据权限
export const updateDataPermission = (data: DataPermissionItem) => {
  return defHttp.put({ url: Api.DataPermissionUpdate, data });
};

// 删除数据权限
export const deleteDataPermission = (id: number) => {
  return defHttp.delete({ url: `${Api.DataPermissionDelete}?id=${String(id)}` });
};

// 获取数据权限详情
export const getDataPermissionDetail = (id: number) => {
  return defHttp.get({ url: Api.DataPermissionDetail, params: { id } });
};

// 角色权限管理相关接口

// 获取角色权限列表
export const getRolePermissionList = (params?: any) => {
  return defHttp.get({ url: Api.RolePermissionList, params });
};

// 创建角色权限
export const createRolePermission = (data: RolePermissionItem) => {
  return defHttp.post({ url: Api.RolePermissionCreate, data });
};

// 更新角色权限
export const updateRolePermission = (data: RolePermissionItem) => {
  return defHttp.post({ url: Api.RolePermissionUpdate, data });
};

// 删除角色权限
export const deleteRolePermission = (id: number) => {
  return defHttp.post({ url: Api.RolePermissionDelete, data: { id } });
};

// 获取角色权限详情
export const getRolePermissionDetail = (id: number) => {
  return defHttp.get({ url: Api.RolePermissionDetail, params: { id } });
};

// API Key管理相关接口

// 获取API Key列表
export const getApiKeyList = (params?: any) => {
  return defHttp.get({ url: Api.ApiKeyList, params });
};

// 创建API Key
export const createApiKey = (data: Omit<ApiKeyItem, 'id'>) => {
  return defHttp.post({ url: Api.ApiKeyCreate, data });
};

// 更新API Key
export const updateApiKey = (data: ApiKeyItem) => {
  return defHttp.put({ url: Api.ApiKeyUpdate, data });
};

// 删除API Key
export const deleteApiKey = (id: number) => {
  return defHttp.delete({ url: `${Api.ApiKeyDelete}?id=${id}` });
};

// 获取API Key详情
export const getApiKeyDetail = (id: number) => {
  return defHttp.get({ url: Api.ApiKeyDetail, params: { id } });
};
// 执行sql
export const executeSql = (data: any) => {
  return defHttp.post({ url: Api.executeSql, data });
};

// 获取角色权限列表
export const getTeamInfoApi = () => {
  return defHttp.get({ url: Api.teamInfo });
};

export const temamEditApi = (params?: any) => {
  return defHttp.put({ url: Api.temamEdit, params });
};

export const temamAddApi = (params?: any) => {
  return defHttp.post({ url: Api.temamAdd, params });
};

// teamMember

export const getTeamMemberApi = (teamId) => {
  return defHttp.get({ url: Api.teamMember + '/' + teamId });
};

export const teamAddUserApi = (params?: any) => {
  return defHttp.post({ url: Api.teamAddUser, params });
};

export const teamUpdateMemberApi = (data) => {
  return defHttp.put({ url: Api.teamUpdateMember, data });
};
export const teamRemoveMemberApi = (params) => {
  return defHttp.delete({ url: Api.teamRemoveMember, params });
};
export const teamAwitchApi = (id) => {
  return defHttp.post({ url: Api.switch + '/' + id });
};
// switch;  deleteTeamApi

export const deleteTeamApi = (id) => {
  return defHttp.delete({ url: `${Api.deleteTeam}?id=${id}` });
};
