<template>
  <div class="apikey-management-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2 class="page-title">密钥管理</h2>
      </div>
      <div class="header-right">
        <el-button
          type="primary"
          :icon="Plus"
          @click="handleAdd"
          :disabled="apiKeyList.length >= 20"
          data-testid="create-button"
        >
          创建API-KEY
        </el-button>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
      <el-table
        v-loading="tableLoading"
        :data="apiKeyList"
        border
        style="width: 100%"
        :empty-text="emptyText"
      >
        <el-table-column prop="id" label="ID" min-width="180" />
        <el-table-column prop="apiKey" label="API Key" min-width="150">
          <template #default="{ row }">
            <div class="api-key-cell">
              <span class="api-key-text">{{ formatApiK<PERSON>(row.apiKey) }}</span>
              <el-button
                type="text"
                :icon="DocumentCopy"
                @click="copyApiKey(row.apiKey)"
                title="复制"
              />
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="teamName" label="归属团队" min-width="200" />
        <el-table-column prop="description" label="描述" min-width="300" />
        <el-table-column prop="createTime" label="创建时间" min-width="200" />
        
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button type="primary" link @click="handleEdit(row)">编辑</el-button>
              <el-popconfirm
                title="确认删除吗？"
                confirm-button-text="确认"
                cancel-button-text="取消"
                @confirm="handleDelete(row)"
              >
                <template #reference>
                  <el-button type="danger" link>删除</el-button>
                </template>
              </el-popconfirm>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 添加/编辑弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="500px"
      :close-on-click-modal="false"
      :destroy-on-close="true"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="80px"
        label-position="top"
      >
        <el-form-item label="归属团队" prop="teamId" required>
          <el-select
            v-model="formData.teamId"
            placeholder="请选择团队"
            style="width: 100%"
            :disabled="isEdit"
          >
            <el-option
              v-for="team in teamOptions"
              :key="team.value"
              :label="team.label"
              :value="team.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="描述" prop="description">
          <el-input
            v-model="formData.description"
            type="textarea"
            :rows="3"
            placeholder="请输入描述"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            :loading="submitLoading"
            @click="handleSubmit"
          >
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, DocumentCopy } from '@element-plus/icons-vue'
import {
  getApiKeyList,
  getTeamInfoApi,
  createApiKey,
  updateApiKey,
  deleteApiKey
} from '/@/views/team/api/index'
import { ApiKeyItem } from '/@/views/team/types'

// 响应式数据
const apiKeyList = ref<ApiKeyItem[]>([])
const tableLoading = ref(false)
const dialogVisible = ref(false)
const submitLoading = ref(false)
const isEdit = ref(false)
const formRef = ref()

// 分页数据
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 表单数据
const formData = ref<Partial<ApiKeyItem>>({
  teamId: '',
  description: ''
})

// 团队选项（模拟数据，实际应该从API获取）
const teamOptions = ref<{ label: string; value: string }[]>([])


// 表单验证规则
const formRules = {
  teamId: [
    { required: true, message: '请选择归属团队', trigger: 'change' }
  ],
  description: [
    { max: 200, message: '描述不能超过200个字符', trigger: 'blur' }
  ]
}

// 计算属性
const dialogTitle = computed(() => isEdit.value ? '编辑API-KEY' : '创建API-KEY')
const emptyText = computed(() => tableLoading.value ? '加载中...' : '暂无数据')

// 格式化API Key显示
const formatApiKey = (apiKey: string) => {
  if (!apiKey) return ''
  if (apiKey.length <= 8) return apiKey
  return `${apiKey.substring(0, 4)}****${apiKey.substring(apiKey.length - 4)}`
}

// 格式化时间
const formatTime = (time: string) => {
  if (!time) return ''
  return new Date(time).toLocaleString('zh-CN')
}

// 复制API Key
const copyApiKey = async (apiKey: string) => {
  try {
    await navigator.clipboard.writeText(apiKey)
    ElMessage.success('API Key已复制到剪贴板')
  } catch (error) {
    // 降级方案
    const textArea = document.createElement('textarea')
    textArea.value = apiKey
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)
    ElMessage.success('API Key已复制到剪贴板')
  }
}
const getTeamInfo = async () => {
    const res = await getTeamInfoApi();
    teamOptions.value = res.allTeams.map((item: any) => ({
      label: item.name,
      value: item.id
    }));
    // await loadApiKeyList(); // 团队信息加载后再加载列表
}
// 加载API Key列表
const loadApiKeyList = async () => {
  try {
    tableLoading.value = true
    const params = {
      pageNo: pagination.currentPage,
      pageSize: pagination.pageSize
    }

  // 先加载团队信息，确保团队选项已准备好
  await getTeamInfo()
  
  // 调用真实API获取列表
  const response = await getApiKeyList(params)
  apiKeyList.value = response.list || []
  pagination.total = response.total || 0
  } catch (error) {
    console.error('加载API Key列表失败:', error)
    ElMessage.error('加载数据失败')
    apiKeyList.value = []
  } finally {
    tableLoading.value = false
  }
}

// 处理添加
const handleAdd = () => {
  isEdit.value = false
  formData.value = {
    teamId: '',
    description: ''
  }
  dialogVisible.value = true
}

// 处理编辑
const handleEdit = (row: ApiKeyItem) => {
  isEdit.value = true
  formData.value = {
    teamId: String(row.teamId),
    description: row.description
  }
  dialogVisible.value = true
}

// 处理删除
const handleDelete = async (row: ApiKeyItem) => {
  try {
    await deleteApiKey(row.id!)
    ElMessage.success('删除成功')
    loadApiKeyList()
  } catch (error) {
    console.error('删除失败:', error)
    ElMessage.error('删除失败')
  }
}
// 处理提交
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    submitLoading.value = true

    if (isEdit.value) {
      await updateApiKey(formData.value as ApiKeyItem)
      ElMessage.success('更新成功')
      dialogVisible.value = false
      loadApiKeyList()
    } else {
      await createApiKey(formData.value as Omit<ApiKeyItem, 'id'>)
      ElMessage.success('创建成功')
      dialogVisible.value = false
      loadApiKeyList()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('提交失败:', error)
      ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
    }
  } finally {
    submitLoading.value = false
  }
}

// 分页处理
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.currentPage = 1
  loadApiKeyList()
}

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
  loadApiKeyList()
}

// 组件挂载时加载数据
onMounted(() => {
  loadApiKeyList()
})
</script>

<style scoped lang="scss">
.apikey-management-container {
  padding: 20px;
  background-color: #fff;
  border-radius: 8px;
  border: 1px solid #e6e8ee;
  height: calc(100vh - 82px);
  overflow: auto;
  margin-right: 8px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e6e8ee;

  .header-left {
    .page-title {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #1f2937;
    }
  }

  .header-right {
    .el-button {
      border-radius: 6px;
    }
  }
}

.table-container {
  .el-table {
    // border-radius: 8px;
    overflow: hidden;
    // border: 1px solid #e6e8ee;

    :deep(.el-table__header) {
    //   background-color: #f9fafb;

      th {
        // background-color: #f9fafb;
        color: #374151;
        font-weight: 600;
        border-bottom: 1px solid #e6e8ee;
      }
    }

    :deep(.el-table__body) {
      tr {
        &:hover {
          background-color: #f9fafb;
        }
      }

      td {
        border-bottom: 1px solid #f3f4f6;
      }
    }
  }

  .api-key-cell {
    display: flex;
    align-items: center;
    gap: 8px;

    .api-key-text {
      // font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-size: 13px;
      color: #374151;
      // background-color: #f3f4f6;
      // padding: 2px 6px;
      border-radius: 4px;
      flex: 1;
    }

    .el-button {
      padding: 4px;
      margin: 0;

      &:hover {
        color: #1a5afe;
      }
    }
  }

  .action-buttons {
    display: flex;
    gap: 8px;

    .el-button {
      padding: 0;
      margin: 0;
      font-size: 14px;
    }
  }
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
  padding-top: 16px;
  border-top: 1px solid #f3f4f6;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

// 响应式设计
@media (max-width: 768px) {
  .apikey-management-container {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;

    .header-right {
      width: 100%;

      .el-button {
        width: 100%;
      }
    }
  }

  .table-container {
    .el-table {
      :deep(.el-table__body-wrapper) {
        overflow-x: auto;
      }
    }
  }

  .pagination-container {
    justify-content: center;

    .el-pagination {
      :deep(.el-pagination__sizes),
      :deep(.el-pagination__jump) {
        display: none;
      }
    }
  }
}

// 空状态样式
.el-table :deep(.el-table__empty-block) {
  padding: 60px 0;

  .el-table__empty-text {
    color: #9ca3af;
    font-size: 14px;
  }
}

// 加载状态样式
.el-table :deep(.el-loading-mask) {
  background-color: rgba(255, 255, 255, 0.8);
}
</style>