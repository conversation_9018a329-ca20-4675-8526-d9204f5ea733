<template>
  <div class="personalcenter-container">
    <div class="main">
      <!-- MCP工具管理  智能体配置  智能体编排 -->

      <div class="sidebar">
        <h2 class="sidebar-title">个人中心</h2>
        <div class="divider"></div>
        <div class="menu-list">
          <div
            v-for="(item, index) in menuList"
            :key="item.id"
            @click="selectItem(index)"
            :class="['menu-item', { active: selectedIndex === index }]"
          >
            <span class="menu-label">{{ item.label }}</span>
          </div>
        </div>
      </div>
      <div class="content">
        <basic-form
          :isCreate="false"
          :form-list="userSchema"
          :formData="formData"
          @success="submitData"
          v-if="selectedIndex === 0"
        >
          <template #autocomplete>
            <el-autocomplete
              style="width: 100%"
              v-model="formData.email"
              :fetch-suggestions="querySearchEmail"
              :trigger-on-focus="false"
              placeholder="电子邮箱"
          /></template>
        </basic-form>

        <basic-form
          :isCreate="false"
          :form-list="updatepwdFormSchema"
          :formData="formPassWordData"
          @success="submitPassWordData"
          v-else
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup name="UpdatePwd">
  import { ref, shallowRef } from 'vue';
  // import AgentSet from './agentSet.vue';
  // import Arrange from './arrange.vue';
  // import Tools from './tools.vue';
  // import GlobalConfig from './globalConfig.vue';
  import { getAuthStorage, setAuthStorage } from '/@/utils/storage/auth';
  import { updatepwdFormSchema } from './updatepwd.data';
  import { useLogout } from '/@/hooks/web/useLogout';
  import { UpdatePassword, updateInfo } from '/@/views/shares/api/userSetting';
  import { AesEncryption, cacheCipher } from '/@/utils/cipher';
  import { useMessage } from '/@/hooks/web/useMesage';
  // import { updateInfo } from '/@/views/shares/api/user';
  const userInfo: any = getAuthStorage();
  const encryption = new AesEncryption(cacheCipher);
  const { createMessage } = useMessage();
  console.log(userInfo);
  const menuList = shallowRef([
    {
      label: '基本设置',
      id: 1,
    },
    {
      label: '修改密码',
      id: 2,
    },
  ]);

  const emailReg = /^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/;
  const phoneReg =
    /^((\+86|0086)?\s*)((134[0-8]\d{7})|(((13([0-3]|[5-9]))|(14[5-9])|15([0-3]|[5-9])|(16(2|[5-7]))|17([0-3]|[5-8])|18[0-9]|19(1|[8-9]))\d{8})|(14(0|1|4)0\d{7})|(1740([0-5]|[6-9]|[10-12])\d{7}))$/;
  // import { checkApi } from '../../../upms/api/user';
  const userSchema: any[] = [
    {
      label: '电子邮箱',
      field: 'email',
      slot: 'autocomplete',
      rules: [
        {
          required: true,
          trigger: 'blur',
          pattern: emailReg,
          message: '请输入正确的电子邮箱',
        },
        { max: 100, message: '超过长度限制，最多100字' },
      ],
      required: true,
    },
    {
      field: 'name',
      label: '员工姓名',
      component: 'Input',
      componentProps: {
        placeholder: '请输入姓名',
        disabled: false,
      },
      rules: [{ max: 90, message: '超过长度限制，最多90字' }],
      required: true,
    },
    {
      field: 'username',
      label: '账号',
      component: 'Input',
      componentProps: {
        placeholder: '请输入账号',
        disabled: true,
      },
      rules: [{ max: 90, message: '超过长度限制，最多90字' }],
      required: true,
    },
    {
      label: '手机号',
      field: 'phone',
      component: 'Input',
      componentProps: {
        placeholder: '请输入手机号',
      },
      rules: [
        {
          pattern: phoneReg,
          message: '请输入正确的手机号',
        },
        // {
        //   validator: (rule, value) => {
        //     if (!value) {
        //       return true;
        //     }
        //     return new Promise<void>((resolve, reject) => {
        //       const params = {
        //         type: 2,
        //         value: value,
        //         id: userInfo.sysEmployee.id,
        //       };
        //       checkApi(params).then((res) => {
        //         res ? resolve() : reject(res.message || '手机号码已存在');
        //       });
        //     });
        //   },
        // },
      ],
      // required: true,
    },
    {
      label: '性别：',
      field: 'sex',
      component: 'RadioGroup',
      componentProps: {
        options: [
          {
            label: '男',
            value: '1',
          },
          {
            label: '女',
            value: '2',
          },
        ],
      },
      required: true,
    },

    // {
    //   label: '所属板块：',
    //   field: 'industryType',
    //   component: 'Text',
    // },
  ];

  const formData = ref<any>({
    avatar: userInfo.avatar,
    email: userInfo.email,
    name: userInfo.name,
    phone: userInfo.phone,
    username: userInfo.username,
    sex: userInfo.sex,
  });

  const formPassWordData = ref({
    id: userInfo.id,
    username: userInfo.username,
    oldPassword: '',
    newPassword: '',
    confirmPassword: '',
  });

  const selectedIndex = ref(0); //   0
  // const currentComponent = shallowRef(Arrange); //Arrange
  const selectItem = (index) => {
    selectedIndex.value = index; // 更新选中的索引
    // currentComponent.value = menuList.value[index].value;
  };

  const submitData = (data) => {
    // userInfo = data;
    // userInfo.realName = data.empName;
    //

    console.log(data);
    const { phone, email, name, sex, username } = data;
    // console.log(updateInfo());
    updateInfo({ phone, email, name, sex, username, id: userInfo.id }).then((res) => {
      console.log(res);
      ElMessage({
        type: 'success',
        message: '修改成功',
      });
      setAuthStorage({
        phone,
        email,
        name,
        sex,
        username,
        id: userInfo.id,
        avatar: userInfo.avatar,
        createTime: userInfo.createTime,
        permissions: userInfo.permissions,
        roles: userInfo.roles,
      });
      location.reload();
    });
  };

  // 邮箱自动填充后缀名
  function querySearchEmail(queryString, callback) {
    const emailList = [
      { value: '@chinapost.com.cn' },
      { value: '@qq.com' },
      { value: '@163.com' },
      { value: '@sina.com' },
      { value: '@sohu.com' },
      { value: '@yahoo.com.cn' },
    ];
    let results: string[] = [];
    let queryList: string[] = [];
    emailList.map((item) =>
      queryList.push({ value: queryString.split('@')[0] + item.value }),
    );
    results = queryString ? queryList.filter(createFilter(queryString)) : queryList;
    callback(results);
  }

  // 邮箱填写过滤
  function createFilter(queryString) {
    return (item) => {
      return item.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0;
    };
  }

  const { logout } = useLogout();

  const submitPassWordData = (data) => {
    if (data.newPassword != data.confirmPassword) {
      ElMessage({
        message: '新密码和确认新密码不一致,请重新输入',
        grouping: true,
        type: 'warning',
      });
      return false;
    }

    if (
      data.oldPassword === data.newPassword ||
      data.oldPassword === data.confirmPassword
    ) {
      ElMessage({
        message: '新密码和旧密码不可以一致,请重新输入',
        grouping: true,
        type: 'warning',
      });
      return false;
    }

    UpdatePassword({
      id: userInfo.id,
      userName: userInfo.userName,
      oldPassword: encryption.pwdEncryptByAES(data.oldPassword),
      newPassword: encryption.pwdEncryptByAES(data.newPassword),
      confirmPassword: encryption.pwdEncryptByAES(data.confirmPassword),
    }).then(() => {
      createMessage.success('修改密码成功');
      // 退出登录
      logout();
    });
  };
</script>

<style scoped lang="scss">
  .personalcenter-container {
    height: calc(100vh - 62px);
    border: 1px solid #e6e8ee;
    background-color: #fff;
    border-radius: 8px;
    margin: 0 8px 8px 0;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    .main {
      display: flex;
      flex: 1;
      .sidebar {
        flex: 0 0 180px;
        background: #ffffff;
        border-right: 1px solid #e6e8ee;
        .menu-item {
          display: flex;
          align-items: center;
          padding: 12px 20px;
          // margin: 0 12px;
          // border-radius: 8px;
          cursor: pointer;
          transition: all 0.3s;
          font-size: 14px;
          color: #606266;

          &:hover {
            background-color: #f5f7fa;
          }

          &.active {
            color: #409eff;
            background-color: #ecf5ff;
            border-right: 3px solid #409eff;
          }

          .menu-label {
            flex: 1;
          }
        }

        .sidebar-title {
          font-size: 18px;
          font-weight: bold;
          color: #303133;
          padding: 0 20px;
          margin: 15px 0 15px 0;
        }

        .divider {
          height: 1px;
          background-color: #ebeef5;
          margin-bottom: 15px;
        }
      }
      .content {
        flex: 1;
        overflow: auto;
        padding: 20px;
        // background: #f6f8fa;
      }
    }
  }
</style>
