import { defineStore } from 'pinia';
import router, { removeUserRouter } from '/@/router';
import Ua<PERSON><PERSON><PERSON>, { IResult as UaResult } from 'ua-parser-js';
import { UserInfo, UserReturn } from '/#/store';
import {
  getAuthStorage,
  getAuthToken,
  setAuthStorage,
  setAuthToken,
  setWebModel,
} from '/@/utils/storage/auth';
// import { setPassWordStatusStorage } from '/@/utils/storage/auth';
import { store } from '/@/stores';
import {
  getUserInfo,
  getUserInfoLogin,
  phoneLogin,
  userLogin,
  userLogout,
} from '/@/api/sys';
import { LoginParams, PhoneLoginParams } from '/@/api/model/User';
import { buildMenus } from '/@/router/menus';
import {
  usePermissionStore,
  usePermissionStoreWithOut,
} from '/@/stores/modules/permission';
import { useTenantStoreWithOut } from '/@/stores/modules/tenant';
import { PageEnum } from '/@/enums/pageEnum';
import { useDictStoreWithOut } from '/@/stores/modules/dict';
import { useAppStore } from '/@/stores/modules/app';
import { useTabRoutesStoreWithOut } from '/@/stores/modules/tabRoutes';
import { MicroStore } from '/@/stores/indexMicro';
// import { isMicro } from '/@/utils/operate/micro';
// import { ElNotification } from 'element-plus';
// import { formatTime } from '/@/utils/dateUtil';
// import { h } from 'vue';

export interface UserState {
  userInfo: Nullable<UserInfo>;
  token?: string;
  ua: UaResult;
  roleList: string[];
}

const defaultUserInfo = {
  id: '',
  token: '',
  name: '',
  avatar: '',
  roles: [],
  isSystemAdmin: false,
};

const permissionStoreWithOut = usePermissionStoreWithOut();
const tenantStoreWithOut = useTenantStoreWithOut();
const useDict = useDictStoreWithOut();
const tabRoutesStoreWithOut = useTabRoutesStoreWithOut();
const useApp = useAppStore();
// const handleGo = () => {
//   // alert(123);
//   router.push('/user/personalcenter?tab=editPassWord');
// };
export const useUserStore = defineStore({
  id: 'app-user',
  state: (): UserState => ({
    // user info
    userInfo: { ...defaultUserInfo },
    // token
    token: undefined,
    ua: new UaParser().getResult(),
    roleList: [],
  }),
  getters: {
    getToken(): string {
      // console.log(getAuthToken(), 'getAuthToken()')
      // if (!getAuthToken()) {
      //   return getAuthToken();
      // }
      return this.token || getAuthToken();
    },
    getUserInfo(): UserInfo {
      return this.userInfo && this.userInfo.id !== ''
        ? this.userInfo
        : getAuthStorage();
    },
  },
  actions: {
    getMicroUserInfo(data: UserInfo) {
      // 接收主应用消息，在子应用中进行同步
      this.userInfo = data;
      setAuthStorage(data);
    },
    setUserInfo(payload: Nullable<UserInfo>) {
      // if (isMicro()) return;
      this.userInfo = payload;
      setAuthStorage(payload);
      // 向子应用发送消息 userinfo消息
      // microUserInfo.setMicroData(payload);
    },
    // setUserPassWordStatus(payload: Boolean) {
    //   setPassWordStatusStorage(payload);
    // },
    setToken(token: string | undefined | null) {
      this.token = token ? token : '';
      setAuthToken(this.token);
    },
    resetUserInfo() {
      // this.userInfo = { ...defaultUserInfo };
      this.setUserInfo(null);
      this.setToken(null);
      // this.setUserPassWordStatus(false);
      // 处理退出登录后，再次进入菜单不是所选租户的菜单
      tenantStoreWithOut.setCurTenant({
        id: '',
        tenantName: '',
        tenantAlias: '',
      });
      tenantStoreWithOut.setCurProject({
        id: '',
        projectName: '',
        projectAlias: '',
      });
      // 清除缓存中的Header租户应用信息
      tenantStoreWithOut.setHeaderInfoStorage({});
    },
    async resetData() {
      // 清空用户路由
      removeUserRouter();
      // 清除 menu菜单
      permissionStoreWithOut.setMenuList([]);
      // 刷新字典
      this.getDictStore();
      // 刷新菜单
      await buildMenus();
      // 清除全部tab页面
      tabRoutesStoreWithOut.delAllTabs();
    },
    async refreshUserData() {
      await this.resetData();
      // 打开home页
      // await router.replace(this.getUserInfo.homePath || PageEnum.BASE_HOME);
      // window.location.reload();
      await router.go(0);
    },
    async login(user: LoginParams | PhoneLoginParams, phone = false) {
      // 调用登陆接口
      const permissionStore = usePermissionStore();
      let data;
      if (phone) {
        // 短信登录
        data = await phoneLogin(user as PhoneLoginParams);
      } else {
        // 账号密码登录
        data = await userLogin(user as LoginParams);
      }
      console.log(data);
      const { token } = data;
      setWebModel('simple');
      this.setToken(token);
      // 获取用户信息
      console.log(permissionStore);
      const infoData = await this.getUserInfoLogin();
      console.log('infoData');
      console.log(infoData);
      const userInfo = infoData.info;
      // const paddWordStatus = infoData.needRestPassword;
      this.setUserInfo(userInfo);
      // this.setUserPassWordStatus(paddWordStatus);
      // 获取用户字典, 当使用upms时获取字典
      // this.getDictStore();
      console.log(permissionStore);
      // 处理菜单 获取后端
      if (!permissionStore.getDynamicAddedRoute) {
        await buildMenus();
      }
      router.replace(userInfo.homePath || PageEnum.BASE_HOME);
    },

    getDictStore() {
      // 登录后从后端获取 用户字典
      if (!useApp.getProjectConfig.upms) {
        return;
      }
      useDict.reqDict();
      // if (this.getUserInfo.isSystemAdmin) {
      //   useDict.reqDict();
      // } else {
      //   useDict.reqDict(this.getUserInfo.tenantId, this.getUserInfo.projectId);
      // }
    },

    /**
     * 登录后获取用户信息
     */
    async getUserInfoLogin(): Promise<any> {
      const userReturn: UserReturn = await getUserInfoLogin();
      // userReturn.user.isSystemAdmin = userReturn.isSystemAdmin;
      return {
        info: userReturn,
        // needRestPassword: userReturn.needRestPassword,
        // tipUpdatePasswordFlag: userReturn.tipUpdatePasswordFlag,
        // dayDifference: userReturn.dayDifference,
      };
    },

    // async getUserNeedRestPassword(): Promise<UserInfo> {
    //   const userReturn: UserReturn = await getUserInfoLogin();
    //   userReturn.user.isSystemAdmin = userReturn.isSystemAdmin;
    //   return userReturn.user;
    // },
    /**
     * 刷新后获取用户信息
     */
    async getUserInfoAction(): Promise<any> {
      // alert(1);
      const userReturn: UserReturn = await getUserInfo();
      // userReturn.user.isSystemAdmin = userReturn.isSystemAdmin;
      return {
        info: userReturn,
        // needRestPassword: userReturn.needRestPassword,
        // tipUpdatePasswordFlag: userReturn.tipUpdatePasswordFlag,
        // dayDifference: userReturn.dayDifference,
      };
    },
    /**
     * 刷新用户信息
     */
    async reFreshUser() {
      const user = await this.getUserInfoAction();
      const userInfo = user.info;
      this.setUserInfo(userInfo);
      return userInfo;

      // const infoData = await this.getUserInfoLogin();
      // const paddWordStatus = user.needRestPassword;
      // console.log(paddWordStatus);
      // this.setUserPassWordStatus(paddWordStatus as boolean);
    },
    async logout(status = 0) {
      // 调用退出登陆接口
      if (status !== 401) userLogout();
      await router.push({ name: 'Login' });
      this.resetUserInfo();
      tabRoutesStoreWithOut.delAllTabs();
      permissionStoreWithOut.setDynamicAddedRoute(false);
      // 清空用户路由
      removeUserRouter();
      // 清除 menu菜单
      permissionStoreWithOut.setMenuList([]);
      // 清除字典信息
      useDict.setCodeDict({});
      // 重置初次登录请求状态
      tenantStoreWithOut.setHeaderRequested(false);
    },
    async verification(token: string) {
      // 调用 token 验证接口
      return Promise.resolve(token);
    },
  },
});

/**
 * userinfo的 微应用监听
 */
export const microUserInfo = new MicroStore<UserInfo>(
  'userInfo',
  useUserStore().getMicroUserInfo,
);

// use outside the setup
export function useUserStoreWithOut() {
  return useUserStore(store);
}
