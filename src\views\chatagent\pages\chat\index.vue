<!--
 * Copyright 2025 the original author or authors.
-->
<template>
  <div class="chat-page">
    <div class="app-container">
      <!-- 侧边栏 -->
      <div class="sidebar" :class="{ collapsed: sidebarCollapsed }">
        <div class="sidebar-header">
          <div class="app-title">
            <div class="title-left">
              <!-- <Icon icon="carbon:flow" /> -->
              <span v-if="!sidebarCollapsed" :title="assistantName">{{ assistantName || '' }}</span>
            </div>

            <button class="back-to-dashboard-btn" @click="goToDashboard" v-if="!sidebarCollapsed">
              <Icon icon="carbon:arrow-left" />
              <span>返回</span>
            </button>
          </div>
        </div>
        <!-- 助手和历史会话切换 -->
        <div class="sidebar-tabs" v-if="!sidebarCollapsed">
          <div class="tab-buttons">
            <button
              :class="['tab-button', { active: activeTab === 'assistants' }]"
              @click="activeTab = 'assistants'"
            >
              <Icon icon="carbon:bot" />
              <span>助手</span>
            </button>
            <button
              :class="['tab-button', { active: activeTab === 'history' }]"
              @click="activeTab = 'history'"
            >
              <Icon icon="carbon:chat" />
              <span>历史</span>
            </button>
          </div>
        </div>

        <!-- 助手列表区域 -->
        <div class="assistants-list" v-if="!sidebarCollapsed && activeTab === 'assistants'">
          <div class="assistants-content">
            <div v-if="assistantsLoading" class="loading-placeholder">
              <Icon icon="carbon:time" />
              <span>加载助手列表...</span>
            </div>

            <div v-else-if="assistantsList.length === 0" class="empty-assistants">
              <Icon icon="carbon:bot" />
              <span>暂无助手</span>
            </div>

            <div v-else>
              <div
                v-for="assistant in assistantsList"
                :key="assistant.planTemplateId"
                class="assistant-item"
                @click="selectAssistant(assistant)"
              >
                <!-- <div class="assistant-avatar">
                  <Icon icon="carbon:bot" />
                </div> -->
                <div class="assistant-info">
                  <div class="assistant-name">{{ assistant.title }}</div>
                  <div class="assistant-description">{{ formatTime(assistant.createTime) }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 历史会话区域 -->
        <div class="chat-history" v-if="!sidebarCollapsed && activeTab === 'history'">
          <!-- 新对话按钮 -->
          <div class="new-chat-section">
            <button class="new-chat-btn-full" @click="confirmNewChat">
              <Icon icon="carbon:add" />
              <span>新对话</span>
            </button>
          </div>
          <!-- <div class="history-header">
            <h5>历史对话</h5>
            <button class="btn-icon" @click="refreshHistory" title="刷新历史">
              <Icon icon="carbon:refresh" />
            </button>
          </div> -->

          <div class="history-search">
            <div class="search-input-wrapper">
              <el-input
                type="text"
                class="search-input"
                v-model="historySearchKeyword"
                placeholder="搜索对话..."
                @input="handleSearchInput"
                :prefix-icon="Search"
                clearable
              />
              <!-- <Icon icon="carbon:search" class="search-icon" /> -->
            </div>
          </div>



          <div v-loading="historyLoading" class="history-content">
            <!-- <div v-if="historyLoading" class="loading-placeholder">
              <span>加载历史对话...</span>
            </div> -->

            <div v-if="filteredHistory.length === 0" class="empty-history">
              <!-- <Icon icon="carbon:chat" /> -->
              <span>{{ historySearchKeyword ? '没有找到匹配的对话' : '暂无历史对话' }}</span>
            </div>

            <div v-else>
              <div v-for="group in groupedHistory" :key="group.title" class="history-group">
                <div class="history-group-title">{{ group.title }}</div>
                <div
                  v-for="item in group.items"
                  :key="item.id"
                  class="history-item"
                  :class="{ active: item.id === currentChatId }"
                  @click="loadHistoryChat(item)"
                >
                  <div class="history-item-title">{{ item.title }}</div>
                  <div class="history-item-meta">
                    <span class="history-item-time">{{ formatTime(item.updatedAt) }}</span>
                    <div class="history-item-actions">
                      <el-popconfirm
                        title="确认删除吗？"
                        confirm-button-text="确认"
                        cancel-button-text="取消"
                        @confirm="deleteHistoryItem(item)"
                        @cancel="() => {}"
                      >
                        <template #reference>
                          <button class="history-item-action" @click.stop title="删除">
                            <Icon icon="carbon:trash-can" />
                          </button>
                        </template>
                      </el-popconfirm>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 侧边栏折叠按钮 -->
        <!-- <button class="sidebar-toggle" @click="toggleSidebar">
          <Icon :icon="sidebarCollapsed ? 'carbon:chevron-right' : 'carbon:chevron-left'" />
        </button> -->
      </div>

      <!-- 主内容区域 -->
      <div class="main-content" :class="{ 'with-right-panel': showRightPanel }">
        <div class="chat-container" ref="chatContainerRef">
          <!-- 系统欢迎消息 -->
          <div class="message system-message">
            <!-- <div class="message-avatar">
              <Icon icon="carbon:information" />
            </div> -->
            <div class="message-content">
              欢迎使用AI智能助手！<br>
            </div>
          </div>
          
          <!-- 对话消息将在这里动态渲染 -->
          <div v-for="(round, index) in conversationRounds" :key="round.id" class="conversation-round" :data-round-id="round.id">
            <!-- 用户消息 -->
            <div class="message user-message">
              <div class="message-avatar">
                <Icon icon="carbon:user"  />
              </div>
              <div class="message-content">
                {{ round.userMessage }}
              </div>
            </div>
            <!-- 只在最新的会话轮次中显示加载提示 -->
            <div v-if="sending && index === conversationRounds.length - 1" class="loading-text-shimmer">稍等，正在分析中...</div>
            <!-- AI回复容器 -->
            <div class="ai-response-container">
              <div class="ai-response-content">
                <!-- 新的执行步骤容器 - 基于EXECUTION_RESPONSE事件，按Agent分组 -->
                <div v-if="round.executionSteps && round.executionSteps.length > 0" class="execution-steps-container">
                  <ChatAgentGroupContainer
                    v-for="(agentGroup, index) in groupedExecutionSteps(round.executionSteps)"
                    :key="agentGroup.agentExecutionId"
                    :ref="(el) => { if (el) setAgentGroupRef(el, agentGroup.agentExecutionId) }"
                    :agent-group="agentGroup"
                    :agent-index="index"
                    :total-agents="groupedExecutionSteps(round.executionSteps).length"
                    :is-last-agent="index === groupedExecutionSteps(round.executionSteps).length - 1"
                    :clear-highlight-signal="clearHighlightSignal"
                    @tool-click="handleToolClick"/>
                  <div class="step-container-summary" v-if="round.planSummary || round.summaryContent || (round.chartData && round.chartData.length > 0)">
                    <!-- 图表和表格显示区域 -->
                    <div v-if="round.chartData && round.chartData.length > 0" class="summary-charts-container">
                      <div
                        v-for="(chartItem, index) in round.chartData"
                        :key="`chart-${chartItem.stepId}-${index}`"
                        class="summary-chart-item"
                        :data-chart-type="chartItem.chartType"
                      >
                        <!-- 多视图切换容器 -->
                        <div class="summary-multi-view-container">
                          <!-- <div class="chart-title">{{ chartItem.title }}</div> -->

                          <!-- Tab切换按钮 -->
                          <div class="chart-view-tabs">
                            
                            <button
                              :class="['tab-icon-button', { active: getActiveChartTab(chartItem.stepId) === 'bar' }]"
                              @click="setActiveChartTab(chartItem.stepId, 'bar')"
                              title="柱状图"
                            >
                              <el-icon><Histogram /></el-icon>
                            </button>
                            
                            <button
                              :class="['tab-icon-button', { active: getActiveChartTab(chartItem.stepId) === 'line' }]"
                              @click="setActiveChartTab(chartItem.stepId, 'line')"
                              title="折线图"
                            >
                              <el-icon><TrendCharts /></el-icon>
                            </button>
                            <button
                              :class="['tab-icon-button', { active: getActiveChartTab(chartItem.stepId) === 'pie' }]"
                              @click="setActiveChartTab(chartItem.stepId, 'pie')"
                              title="饼图"
                            >
                              <el-icon><PieChart /></el-icon>
                            </button>
                            <button
                              :class="['tab-icon-button', { active: getActiveChartTab(chartItem.stepId) === 'table' }]"
                              @click="setActiveChartTab(chartItem.stepId, 'table')"
                              title="表格视图"
                            >
                              <el-icon><Grid /></el-icon>
                            </button>
                          </div>

                          <!-- 表格视图 -->
                          <div v-show="getActiveChartTab(chartItem.stepId) === 'table'" class="summary-table-container">
                            <div class="chart-title">{{ chartItem.title }}</div>
                            <VTable
                              :data="chartItem.data"
                              :columns="getChartTableColumns(chartItem.data)"
                              height="300px"
                            />
                          </div>

                          <!-- 图表视图 -->
                          <div v-show="getActiveChartTab(chartItem.stepId) !== 'table'" class="summary-chart-container">
                            <div
                              :id="`summary-chart-${chartItem.stepId}`"
                              class="summary-chart-canvas"
                              style="width: 100%; height: 350px;"
                            ></div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- 总结内容 -->
                    <div v-if="round.planSummary || round.summaryContent" class="summary-content">
                      <!-- <strong>总结：</strong> -->
                      <div v-html="renderMarkdown(round.planSummary || round.summaryContent)"></div>
                    </div>
                  </div>
                  
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 输入区域 -->
        <div class="chat-input">
          <!-- 数据源和模型选择区域 -->
        <div class="chat-config" >
          <div class="config-row">
            <!-- 数据源级联选择 -->
            <div class="config-item datasource-config">
              <!-- <label class="config-label">数据源:</label> -->
              <DataSourceCascader
                v-model="dataSourceSelection"
                @update:modelValue="onDataSourceSelectionChange"
              />
            </div>
          </div>
        </div>
          <div class="input-container">
            <div class="message-input-wrapper">
              <textarea
                class="message-input"
                v-model="messageInput"
                placeholder="输入您的问题"
                rows="1"
                @keydown="handleInputKeydown"
                @input="adjustTextareaHeight"
                ref="messageInputRef"
              ></textarea>
            </div>
            <button
              :class="['send-btn', { 'stop-mode': sending }]"
              :disabled="!sending && !messageInput.trim()"
              @click="handleButtonClick"
              type="button"
            >
              <Icon  :icon="sending ? 'carbon:stop' : 'carbon:send'" />
              <span>{{ sending ? '停止' : '发送' }}</span>
            </button>
          </div>
          <!-- 大模型选择 -->
            <div class="config-item model-config">
              <ModelSelect
                v-model="selectedModelId"
                :model-list="modelList"
                :disabled="sending"
              />
            </div>
        </div>
      </div>

      <!-- 右侧工具详情面板 -->
      <div v-if="showRightPanel" class="right-panel" :style="{ width: rightPanelWidth + 'px' }">
        <!-- 拖拽调整大小的把手 -->
        <div 
          class="resize-handle" 
          @mousedown="startResize" 
          @touchstart="startResize"
        ></div>
        
        <div class="panel-header">
          <div class="panel-title">
            <Icon icon="carbon:tool-box" />
            <span>调用工具: {{ selectedTool.toolName }}</span>
          </div>
          <button class="close-btn" @click="closeRightPanel">
            <Icon icon="carbon:close" />
          </button>
        </div>

        <div class="panel-content">
          <!-- 加载状态 -->
          <div v-if="toolDetailLoading" class="loading-section">
            <div class="loading-spinner">
              <Icon icon="carbon:circle-dash" class="spinning" />
              <span>正在获取工具详情...</span>
            </div>
          </div>

          <!-- 错误提示 -->
          <div v-if="selectedTool.errorMessage" class="error-section">
            <div class="error-message">
              <Icon icon="carbon:warning" />
              <span>{{ selectedTool.errorMessage }}</span>
            </div>
          </div>

          <!-- 工具详情内容 -->
          <div v-if="!toolDetailLoading">
            <div class="tool-detail-section">
              <h4>工具参数</h4>
              <div class="json-viewer">
               <pre>{{ selectedTool.parameters }}</pre> 
                <!-- <pre>{{ formatToolJson(selectedTool.parameters) }}</pre> -->
              </div>
            </div>

            <div v-if="selectedTool.result" class="tool-detail-section">
              <h4>执行结果</h4>
              <!-- {{ getResultRenderType(selectedTool.result) }} -->
                
              <!-- 智能渲染不同类型的结果 -->
              <div v-if="getResultRenderType(selectedTool.result) === 'table'" class="result-table-container">
                <VTable
                  :data="getTableData(selectedTool.result)"
                  :columns="getTableColumns(selectedTool.result)"
                  height="400px"
                />
              </div>

              <div v-else-if="getResultRenderType(selectedTool.result) === 'chart'" class="result-chart-container">
                <div
                  :id="`tool-chart-${selectedTool.stepId}`"
                  class="chart-canvas"
                  style="width: 100%; height: 400px;"
                ></div>
              </div>

              <div v-else-if="getResultRenderType(selectedTool.result) === 'sql'" class="result-sql-container">
                <div class="sql-header">
                  <Icon icon="carbon:sql" />
                  <!-- <span >SQL语句</span> -->
                </div>
                <div class="sql-content markdown-body">
                  <pre class="sql-code" v-html="renderMarkdown(getSqlMessage(selectedTool.result))"></pre>
                </div>
              </div>

              <div v-else-if="getResultRenderType(selectedTool.result) === 'markdown'" class="result-markdown-container">
                <div class="markdown-header">
                  <Icon icon="carbon:document" />
                  <!-- <span>Markdown内容</span> -->
                </div>
                <div class="markdown-content markdown-body" v-html="renderMarkdown(getMarkdownContent(selectedTool.result))"></div>
              </div>

              <div v-else class="json-viewer">
                <pre>{{ formatToolJson(selectedTool.result) }}</pre>
              </div>
            </div>
          </div>

          <!-- <div class="tool-detail-section">
            <h4>执行信息</h4>
            <div class="info-grid">
              <div class="info-item">
                <span class="label">步骤ID:</span>
                <span class="value">{{ selectedTool.stepId }}</span>
              </div>
              <div class="info-item">
                <span class="label">状态:</span>
                <span class="value" :class="getToolStatusClass(selectedTool.status)">
                  {{ getToolStatusText(selectedTool.status) }}
                </span>
              </div>
            </div>
          </div> -->
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount, nextTick, watch, triggerRef } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Icon } from '@iconify/vue'
import { ElMessage } from 'element-plus'
import dayjs from 'dayjs'
import * as echarts from 'echarts'
import { Marked } from 'marked'
import hljs from 'highlight.js'
import { markedHighlight } from "marked-highlight";
import 'highlight.js/styles/xcode.css'
import ModelSelect from '../../components/common/ModelSelect.vue'
// import ChatPlanContainer from '@/components/chat/ChatPlanContainer.vue'
// import ChatAgentContainer from '../../components/chat/ChatAgentContainer.vue'
// import ChatThinkContainer from '../../components/chat/ChatThinkContainer.vue'
// import ChatToolContainer from '../../components/chat/ChatToolContainer.vue'
// import ChatExecutionStepContainer from '@/components/chat/ChatExecutionStepContainer.vue'
import ChatAgentGroupContainer from '../../components/chat/ChatAgentGroupContainer.vue'
import DataSourceCascader from '../../components/DataSourceCascader.vue'
import VTable from '../../components/VTable.vue'
import { DirectApiService, type DirectStreamingRequest, type DirectStreamEvent } from '../../api/direct-api-service'
// import { ToolDetailApiService, type ToolDetailResponse } from '../../api/tool-detail-api-service'
import { DataSourceApiService } from '../../api/datasource-api-service'
import { type Model } from '../../api/model-api-service'
import { AxiosApiService } from '../../api/axios-api-service'
import { CommonApiService } from '../../api/common-api-service'

import { Grid, Histogram, TrendCharts, PieChart, Search } from '@element-plus/icons-vue'
const route = useRoute()
const router = useRouter()

// 响应式数据
const sidebarCollapsed = ref(false)
// const currentUserId = ref('test-user-001')
// const editingUserId = ref(false)
// const newUserId = ref('')
const historyLoading = ref(false)
const historySearchKeyword = ref('')
const messageInput = ref('')
const sending = ref(false)
// 流式请求控制器，用于停止请求
const currentAbortController = ref<AbortController | null>(null)
// 当前执行的planId，用于停止执行
const currentPlanId = ref<string | null>(null)
interface ConversationRound {
  id: string
  userMessage?: string
  timestamp?: Date
  isLoading?: boolean
  isError?: boolean
  planData?: any
  agentData?: any[]
  thinkData?: any[]
  toolData?: any[]
  executionSteps?: any[]
  planSummary?: string
  summary?: string
  [key: string]: any
}
const conversationRounds = ref<ConversationRound[]>([])

// Tab切换相关
const activeTab = ref('history') // 默认显示历史会话
const assistantName = ref('')

// 助手相关数据
const assistantsLoading = ref(false)
const assistantsList = ref<any>([])
const selectedAssistant = ref(null)


// 搜索防抖
let searchTimeout: any = undefined

// 聊天相关数据
const currentChatId = ref('chat-' + Date.now() + '-' + Math.random().toString(36).substring(2))
const currentChatTitle = ref('对话')
const workflowName = ref('')
// 拖拽调整大小相关变量和方法
const isResizing = ref(false)
const startX = ref(0)
const startWidth = ref(0)
const DEFAULT_PANEL_WIDTH = 500 // 定义默认宽度常量
// 右侧面板相关数据
const showRightPanel = ref(false)
const toolDetailLoading = ref(false)
const rightPanelWidth = ref(DEFAULT_PANEL_WIDTH) // 使用默认宽度常量
const selectedTool = ref({
  stepId: '',
  toolName: '',
  parameters: null,
  result: null,
  status: '',
  startTime: '',
  endTime: '',
  thinkActId: '',
  errorMessage: ''
})



/**
 * 节流函数 - 用于优化拖拽性能
 * @param {Function} fn - 需要节流的函数
 * @param {number} delay - 延迟时间(ms)
 * @returns {Function} - 节流后的函数
 */
const throttle = (fn: Function, delay: number) => {
  let lastCall = 0
  return function(...args: any[]) {
    const now = Date.now()
    if (now - lastCall >= delay) {
      lastCall = now
      fn(...args)
    }
  }
}



/**
 * 处理拖拽过程中的大小调整
 * @param {MouseEvent|TouchEvent} e - 鼠标或触摸事件
 */
const handleResize = (e: MouseEvent | TouchEvent) => {
  if (!isResizing.value) return
  
  // 使用requestAnimationFrame优化性能，减少卡顿
  requestAnimationFrame(() => {
    let clientX = 0
    
    // 处理鼠标事件
    if (e instanceof MouseEvent) {
      clientX = e.clientX
    } 
    // 处理触摸事件
    else if (e instanceof TouchEvent && e.touches.length > 0) {
      clientX = e.touches[0].clientX
    }
    
    // 计算新宽度（从右侧向左拖动，所以是减法）
    const windowWidth = window.innerWidth
    const deltaX = startX.value - clientX
    const newWidth = Math.min(Math.max(startWidth.value + deltaX, 300), windowWidth -60 )
    
    rightPanelWidth.value = newWidth
  })
}

// 存储当前使用的节流函数引用
let currentThrottledResize: Function | null = null

/**
 * 开始拖拽调整大小
 * @param {MouseEvent|TouchEvent} e - 鼠标或触摸事件
 */
const startResize = (e: MouseEvent | TouchEvent) => {
  isResizing.value = true
  
  // 处理鼠标事件
  if (e instanceof MouseEvent) {
    startX.value = e.clientX
  } 
  // 处理触摸事件
  else if (e instanceof TouchEvent && e.touches.length > 0) {
    startX.value = e.touches[0].clientX
  }
  
  startWidth.value = rightPanelWidth.value
  
  // 创建节流版本的handleResize函数
  currentThrottledResize = throttle(handleResize, 16) // 约60fps的频率
  
  // 添加事件监听
  document.addEventListener('mousemove', currentThrottledResize as any)
  document.addEventListener('touchmove', currentThrottledResize as any)
  document.addEventListener('mouseup', stopResize)
  document.addEventListener('touchend', stopResize)
  
  // 添加调整大小时的样式
  document.body.style.cursor = 'ew-resize'
  document.body.style.userSelect = 'none'
}

/**
 * 停止拖拽调整大小
 */
const stopResize = () => {
  if (!isResizing.value) return
  
  isResizing.value = false
  
  // 移除事件监听
  if (currentThrottledResize) {
    document.removeEventListener('mousemove', currentThrottledResize as any)
    document.removeEventListener('touchmove', currentThrottledResize as any)
    currentThrottledResize = null
  }
  
  document.removeEventListener('mouseup', stopResize)
  document.removeEventListener('touchend', stopResize)
  
  // 恢复默认样式
  document.body.style.cursor = ''
  document.body.style.userSelect = ''
}

// 工具高亮状态管理
const currentHighlightedTool = ref<{
  stepId: string
  agentExecutionId: number
} | null>(null)
const clearHighlightSignal = ref(0)

// 新对话设置
const newChatSettings = ref({
  planTemplateId: '',
  userId: '',
  chatName: ''
})

// 数据源和模型选择相关数据
const modelList = ref<Model[]>([])
const selectedModelId = ref<string>('')

// 数据源级联选择
const dataSourceSelection = ref<{
  dataSourceId?: number
  dataSourceName?: string
  databaseName?: string
  schemaName?: string
}>({})

// 聊天历史项接口
interface ChatHistoryItem {
  id: any
  title: string
  updatedAt: Date
  workflowId: string
  modelId?: string
  toolContext?: string
}

// 历史对话数据
const chatHistory = ref<ChatHistoryItem[]>([])

// 页签状态管理
const activeTabsMap = ref(new Map())

// DOM引用
const chatContainerRef = ref()
const agentGroupRefs = ref(new Map())
const messageInputRef = ref()

// 图表管理
const chartInstances = ref(new Map())
// 跟踪已渲染的图表，避免重复渲染
const renderedCharts = ref(new Set())

// 多视图Tab管理
const activeChartTabs = ref(new Map()) // 存储每个图表的当前激活tab

// 多视图Tab管理函数
const getActiveChartTab = (stepId: string) => {
  return activeChartTabs.value.get(stepId) || 'bar' // 默认显示柱状图
}

const setActiveChartTab = (stepId: string, tabType: string) => {
  activeChartTabs.value.set(stepId, tabType)

  // 如果切换到图表视图，需要重新渲染图表
  if (tabType !== 'table') {
    nextTick(() => {
      const containerId = `summary-chart-${stepId}`
      const renderKey = `${containerId}-${tabType}`

      // 查找对应的图表数据
      for (const round of conversationRounds.value) {
        if (round.chartData && round.chartData.length > 0) {
          const chartItem = round.chartData.find(item => item.stepId === stepId)
          if (chartItem) {
            // 清除之前的渲染缓存
            const oldKeys = Array.from(renderedCharts.value).filter(key => key.startsWith(containerId))
            oldKeys.forEach(key => renderedCharts.value.delete(key))

            // 强制重新渲染图表，使用指定的图表类型
            const modifiedChartItem = { ...chartItem, chartType: tabType }
            renderSummaryChart(modifiedChartItem, containerId)

            // 标记为已渲染
            renderedCharts.value.add(renderKey)
            break
          }
        }
      }
    })
  }
}

// 计算属性
const filteredHistory = computed(() => {
  // 如果没有搜索关键词，显示所有历史
  if (!historySearchKeyword.value.trim()) {
    return chatHistory.value
  }

  // 如果有搜索关键词，进行本地过滤（作为后备）
  const keyword = historySearchKeyword.value.toLowerCase()
  return chatHistory.value.filter(item =>
    item.title.toLowerCase().includes(keyword)
  )
})

const groupedHistory = computed(() => {
  const groups: { title: string; items: ChatHistoryItem[] }[] = []
  const today = dayjs()

  const todayItems = filteredHistory.value.filter(item =>
    dayjs(item.updatedAt).isSame(today, 'day')
  )
  const yesterdayItems = filteredHistory.value.filter(item =>
    dayjs(item.updatedAt).isSame(today.subtract(1, 'day'), 'day')
  )
  const olderItems = filteredHistory.value.filter(item =>
    dayjs(item.updatedAt).isBefore(today.subtract(1, 'day'), 'day')
  )

  if (todayItems.length > 0) {
    groups.push({ title: '今天', items: todayItems })
  }
  if (yesterdayItems.length > 0) {
    groups.push({ title: '昨天', items: yesterdayItems })
  }
  if (olderItems.length > 0) {
    groups.push({ title: '更早', items: olderItems })
  }

  return groups
})

// 方法
const toggleSidebar = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value
}

const confirmNewChat = () => {
  // 生成新的chatId
  currentChatId.value = 'chat-' + Date.now() + '-' + Math.random().toString(36).substring(2)
  currentChatTitle.value = '新对话'
  showRightPanel.value = false
  // 清空对话
  conversationRounds.value = []

  // 重置选择状态
  // dataSourceSelection.value = {}
  // selectedModelId.value = ''
}

// 数据源选择变化处理
const onDataSourceSelectionChange = (selection: {
  dataSourceId?: number
  dataSourceName?: string
  databaseName?: string
  schemaName?: string
}) => {
  dataSourceSelection.value = selection
}

// 加载大模型列表
const loadModelList = async () => {
  try {
    modelList.value = await AxiosApiService.getBriefModelList()

    // 尝试设置默认模型
    if (modelList.value.length > 0 && !selectedModelId.value) {
      try {
          // 如果没有默认模型或默认模型不在列表中，选择第一个
          selectedModelId.value = modelList.value[0].id
        
      } catch (error) {
        console.warn('⚠️ 获取默认模型失败，使用第一个模型:', error)
        selectedModelId.value = modelList.value[0].id
      }
    }
  } catch (error) {
    console.error('❌ 加载大模型列表失败:', error)
    modelList.value = []
  }
}

const refreshHistory = async (keyword?: string) => {
  historyLoading.value = true
  try {

    // 获取planTemplateId，优先使用newChatSettings中的，其次使用路由参数
    const planTemplateId = newChatSettings.value.planTemplateId || route.query.id as string

    if (!planTemplateId) {
      console.warn('⚠️ 没有planTemplateId，无法加载历史会话')
      chatHistory.value = []
      return
    }

    const response = await AxiosApiService.getChatSessionsPaginated( planTemplateId,keyword)

    const data = response

    // 转换数据格式
    const historyItems: ChatHistoryItem[] = []
    if (data.sessions && typeof data.sessions === 'object') {
      // 遍历所有分组
      for (const [groupName, sessions] of Object.entries(data.sessions)) {
        if (Array.isArray(sessions)) {
          sessions.forEach(session => {
            const historyItem = {
              id: session.chatId,
              title: session.chatName || session.title || session.userRequest || '未命名对话',
              updatedAt: new Date(session.updatedAt || session.createTime || session.createdAt),
              workflowId: session.planTemplateId || 'unknown',
              toolContext: session.toolContext,
              modelId: session.modelId
            }
            // console.log('📊 转换历史项:', session, '->', historyItem)
            historyItems.push(historyItem)
          })
        }
      }
    }

    // 按时间排序（最新的在前）
    historyItems.sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime())

    chatHistory.value = historyItems
  } catch (error) {
    console.error('❌ 加载历史失败:', error)
    // 保持原有的模拟数据作为后备
    chatHistory.value = []
  } finally {
    historyLoading.value = false
  }
}

const searchHistory = async () => {
  const keyword = historySearchKeyword.value.trim()
  if (!keyword) {
    // 如果搜索关键词为空，重新加载完整历史
    historyLoading.value = true
    await refreshHistory(keyword)
    return
  }
  try {
    // 构建搜索API URL
    // const url = `/api/streaming-events/chat/search?userId=${encodeURIComponent(currentUserId.value)}&keyword=${encodeURIComponent(keyword)}&page=0&size=50`

    // const response = await fetch(url)
    // if (!response.ok) {
    //   throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    // }
    // 获取planTemplateId，优先使用newChatSettings中的，其次使用路由参数
    const planTemplateId = newChatSettings.value.planTemplateId || route.query.id as string

    const data = await AxiosApiService.getChatSessionsPaginated(planTemplateId, keyword)
    
    // 转换搜索结果格式
    const searchResults: ChatHistoryItem[] = []
    if (data.sessions && typeof data.sessions === 'object') {
      // 遍历所有分组
      for (const [groupName, sessions] of Object.entries(data.sessions)) {
        if (Array.isArray(sessions)) {
          sessions.forEach(session => {
            searchResults.push({
              id: session.chatId,
              title: session.chatName || session.title || session.userRequest || '未命名对话',
              updatedAt: new Date(session.updatedAt || session.createTime || session.createdAt),
              workflowId: session.planTemplateId || 'unknown',
              toolContext: session.toolContext,
              modelId: session.modelId
            })
          })
        }
      }
    }

    // 按时间排序（最新的在前）
    searchResults.sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime())

    chatHistory.value = searchResults
  } catch (error) {
    console.error('❌ 搜索失败:', error)
    // 搜索失败时保持当前历史数据
  } finally {
    historyLoading.value = false
  }
}

const loadHistoryChat = async (item) => {
  try {
    // 更新当前聊天信息
    currentChatId.value = item.id
    currentChatTitle.value = item.title
    renderedCharts.value.clear()
    // 解析并回显toolContext到数据源选择器
    if (item.toolContext) {
      try {
        const toolContextData = JSON.parse(item.toolContext)
        console.log('🔧 解析toolContext:', toolContextData)

        // 构建数据源选择对象，只包含有值的字段
        const dataSourceSelectionData: any = {}

        if (toolContextData.dataSourceId !== undefined) {
          dataSourceSelectionData.dataSourceId = toolContextData.dataSourceId
        }
        if (toolContextData.dataSourceName) {
          dataSourceSelectionData.dataSourceName = toolContextData.dataSourceName
        }
        if (toolContextData.databaseName) {
          dataSourceSelectionData.databaseName = toolContextData.databaseName
        }
        if (toolContextData.schemaName) {
          dataSourceSelectionData.schemaName = toolContextData.schemaName
        }

        // 更新数据源选择
        dataSourceSelection.value = dataSourceSelectionData
        console.log('📊 回显数据源选择:', dataSourceSelectionData)
      } catch (parseError) {
        console.warn('⚠️ 解析toolContext失败:', parseError, item.toolContext)
        // 如果解析失败，清空数据源选择
        dataSourceSelection.value = {}
      }
    } else {
      // 如果没有toolContext，清空数据源选择
      dataSourceSelection.value = {}
    }

    // 回显modelId到大模型选择器
    if (item.modelId) {
      selectedModelId.value = item.modelId
      console.log('🤖 回显大模型选择:', item.modelId)
    } else {
      // 如果没有modelId，尝试使用默认模型
      if (modelList.value.length > 0 && !selectedModelId.value) {
        selectedModelId.value = modelList.value[0].id
      }
    }

    // 清空当前对话内容
    conversationRounds.value = []
    showRightPanel.value = false
    // 显示加载状态
    const loadingRound = {
      id: 'loading-' + Date.now(),
      userMessage: '正在加载历史对话...',
      timestamp: new Date(),
      isLoading: true
    }
    conversationRounds.value.push(loadingRound)

    // 加载历史消息
    const response = await AxiosApiService.getChatHistoryById(encodeURIComponent(item.id))
    const data = response

    // 移除加载状态
    conversationRounds.value = []

    // 渲染历史消息
    await renderHistoryMessages(data)

  } catch (error: any) {
    console.error('❌ 加载历史对话失败:', error)

    // 移除加载状态并显示错误
    conversationRounds.value = []

    const errorRound = {
      id: 'error-' + Date.now(),
      userMessage: '加载历史对话失败: ' + error.message,
      timestamp: new Date(),
      isError: true
    }
    conversationRounds.value.push(errorRound)
  }
}

const deleteHistoryItem = async (item) => {
  
  try {

    // 使用正确的删除API端点
    // const url = `/api/streaming-events/chat/session/${encodeURIComponent(item.id)}`

    const data = await AxiosApiService.deleteChatSession(item.id)

    if (data?.deleted) {

      // 如果删除的是当前会话，清空聊天内容
      if (item.id === currentChatId.value) {
        currentChatId.value = ''
        currentChatTitle.value = '层次化对话'
        conversationRounds.value = []
      }

      // 从本地历史中移除
      const index = chatHistory.value.findIndex(h => h.id === item.id)
      if (index > -1) {
        chatHistory.value.splice(index, 1)
      }

    } else {
      throw new Error(data.message || '删除失败')
    }
  } catch (error:any) {
    console.error('❌ 删除会话失败:', error)
    alert('删除失败: ' + error.message)
  }
}

// 处理搜索输入（带防抖）
const handleSearchInput = () => {
  clearTimeout(searchTimeout)
  searchTimeout = setTimeout(() => {
    searchHistory()
  }, 500) // 500ms 防抖延迟
}


// 返回工作台
const goToDashboard = () => {
  router.push({ path: '/dashboard' })
}

// 选择助手
const selectAssistant = (assistant) => {
  selectedAssistant.value = assistant
  
  // 更新assistantName字段的值
  assistantName.value = assistant.title || assistant.name || 'ChatBI'

  // 更新路由上的id和name参数
  const assistantId = assistant.planTemplateId || assistant.id
  const assistantNameForRoute = assistant.title || assistant.name || 'ChatBI'

  if (assistantId) {
    // 更新路由参数，保持当前路径但更新查询参数
    router.replace({
      path: route.path,
      query: {
        ...route.query,
        id: assistantId,
        name: assistantNameForRoute
      }
    })
  }

  // 切换到历史会话tab并加载该助手的会话历史
  activeTab.value = 'history'
  loadAssistantHistory(assistant.planTemplateId)
}

// 加载助手列表
const loadAssistantsList = async () => {
  assistantsLoading.value = true
  try {
    // 从路由参数获取助手ID，如果有的话
    const assistantId = route.params.id
    if (assistantId) {
      // 根据助手ID获取助手列表（这里可能需要调用特定的API）
      assistantsList.value = await AxiosApiService.getAllAgents()
    } else {
      // 如果没有助手ID，获取所有助手
      assistantsList.value = await AxiosApiService.getAllAgents()
    }
  } catch (error) {
    console.error('❌ 加载助手列表失败:', error)
    assistantsList.value = []
  } finally {
    assistantsLoading.value = false
  }
}

// 加载助手的会话历史
const loadAssistantHistory = async (planTemplateId) => {
  historyLoading.value = true
  const keyword = historySearchKeyword.value.trim()
  try {
    // 构建API URL - 根据助手ID获取会话历史
    // const url = `/api/streaming-events/chat/sessions?page=0&size=50&userId=${encodeURIComponent(currentUserId.value)}&planTemplateId=${encodeURIComponent(assistantId)}`

    const response = await AxiosApiService.getChatSessionsPaginated(planTemplateId, keyword)
    // if (!response.ok) {
    //   throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    // }

    const data = response
    // debugger
    // 转换数据格式（与原有的refreshHistory逻辑相同）
    const historyItems: any = []
    if (data.sessions && typeof data.sessions === 'object') {
      for (const [groupName, sessions] of Object.entries(data.sessions)) {
        if (Array.isArray(sessions)) {
          sessions.forEach(session => {
            const historyItem = {
              id: session.chatId,
              title: session.chatName || session.title || session.userRequest || '未命名对话',
              updatedAt: new Date(session.updatedAt || session.createTime || session.createdAt),
              workflowId: session.planTemplateId || 'unknown',
              assistantId: planTemplateId,
              toolContext: session.toolContext,
              modelId: session.modelId
            }
            historyItems.push(historyItem)
          })
        }
      }
    }

    // 按时间排序（最新的在前）
    historyItems.sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime())

    chatHistory.value = historyItems
  } catch (error) {
    console.error('❌ 加载助手会话历史失败:', error)
    chatHistory.value = []
  } finally {
    historyLoading.value = false
  }
}


/**
 * 清空当前对话
 */
const clearChat = () => {
  if (confirm('确定要清空当前对话吗？')) {
    conversationRounds.value = []
    // 清理所有图表实例
    chartInstances.value.forEach((chart) => {
      if ((chart as any)._resizeHandler) {
        window.removeEventListener('resize', (chart as any)._resizeHandler)
      }
      chart.dispose()
    })
    chartInstances.value.clear()
    // 清理所有渲染标记
    renderedCharts.value.clear()
    console.log('对话已清空')
  }
}

/**
 * 强制重新渲染指定图表
 * @param {string} containerId - 容器ID
 */
const forceRerenderChart = (containerId: string) => {
  // 清除渲染标记，允许重新渲染
  renderedCharts.value.delete(containerId)
  console.log('已清除图表渲染标记:', containerId)
}

/**
 * 清除所有图表渲染标记，允许全部重新渲染
 */
const clearAllRenderMarks = () => {
  renderedCharts.value.clear()
  console.log('已清除所有图表渲染标记')
}

// 页签管理函数
const getActiveTab = (roundId: string) => {
  return activeTabsMap.value.get(roundId) || 'chart'
}

const setActiveTab = (roundId: string, tab: string) => {
  activeTabsMap.value.set(roundId, tab)

  // 如果切换到图表页签，需要重新渲染图表
  if (tab === 'chart') {
    nextTick(() => {
      const chartData = getSqlQueryChartData(conversationRounds.value.find(r => r.id === roundId)) as any
      if (chartData && chartData.chartConfig) {
        setTimeout(() => {
          renderChart(chartData.chartConfig, `chart-${roundId}`)
        }, 100)
      }
    })
  }
}

// 工具点击处理
const handleToolClick = async (stepId: string, toolName: string, stepData: any, agentExecutionId?: number) => {

  // 记录当前高亮的工具信息
  currentHighlightedTool.value = {
    stepId: stepId,
    agentExecutionId: agentExecutionId || 0
  }
  // 先显示面板和基本信息
  selectedTool.value = {
    stepId: stepId,
    toolName: toolName,
    parameters: null,
    result: null,
    status: stepData.status,
    startTime: stepData.thinkStartTime,
    endTime: stepData.actEndTime,
    thinkActId: stepId, // 使用stepId作为thinkActId
    errorMessage: ''
  }

  showRightPanel.value = true
  toolDetailLoading.value = true

  try {
    // 调用API获取真实的工具详情
    const toolDetail = await AxiosApiService.getToolDetail(stepId)

    if (toolDetail) {
      // 更新工具详情
      selectedTool.value = {
        ...selectedTool.value,
        parameters: toolDetail.actionDetails?.toolParameters || null,
        result: toolDetail.actionDetails?.actionResult || null,
        status: toolDetail.status,
        startTime: toolDetail.thinkStartTime,
        endTime: toolDetail.actEndTime || toolDetail.thinkEndTime,
        errorMessage: ''
      }
    } else {
      console.warn('⚠️ 未获取到工具详情，使用本地数据')

      // 如果API没有返回数据，使用本地数据作为备选
      selectedTool.value = {
        ...selectedTool.value,
        parameters: stepData.toolInfo?.parameters || null,
        result: stepData.toolInfo?.result || null,
        errorMessage: '工具详情暂不可用，显示本地缓存数据'
      }
    }
  } catch (error: any) {
    console.error('❌ 获取工具详情失败:', error)

    // 发生错误时，使用本地数据作为备选
    selectedTool.value = {
      ...selectedTool.value,
      parameters: stepData.toolInfo?.parameters || null,
      result: stepData.toolInfo?.result || null,
      errorMessage: `获取详情失败: ${error.message}`
    }
  } finally {
    toolDetailLoading.value = false

    // 如果是图表类型，渲染图表
    nextTick(() => {
      if (getResultRenderType(selectedTool.value.result) === 'chart') {
        setTimeout(() => {
          renderToolChart(selectedTool.value.result, `tool-chart-${selectedTool.value.stepId}`)
        }, 100)
      }
    })
  }
}

// 设置AgentGroup组件引用
const setAgentGroupRef = (el: any, agentExecutionId: number) => {
  if (el) {
    agentGroupRefs.value.set(agentExecutionId, el)
  } else {
    agentGroupRefs.value.delete(agentExecutionId)
  }
}

// 关闭右侧面板
const closeRightPanel = () => {
  // 清除工具高亮状态 - 使用信号方式
  if (currentHighlightedTool.value) {
    clearHighlightSignal.value = Date.now() // 使用时间戳作为信号
    currentHighlightedTool.value = null
  } else {
    console.log('🔧 [ChatPage] 没有当前高亮的工具')
  }

  // 关闭面板
  showRightPanel.value = false
  
  // 重置面板宽度为默认值，以便下次打开时恢复默认大小
  setTimeout(() => {
    rightPanelWidth.value = DEFAULT_PANEL_WIDTH
  }, 300) // 等待关闭动画完成后再重置宽度
}

// 格式化工具JSON，支持markdown渲染
const formatToolJson = (obj: any) => {
  if (!obj) return '暂无数据'

  // 如果是字符串且包含markdown标记，直接返回用于markdown渲染
  if (typeof obj === 'string') {
    const isMarkdown = isMarkdownContent(obj)

    if (isMarkdown) {
      return obj // 返回原始字符串用于markdown渲染
    }
  }

  // 检查对象中是否包含markdown内容
  if (typeof obj === 'object' && obj !== null) {
    // 递归检查对象的值
    const checkObjectForMarkdown = (value: any): boolean => {
      if (typeof value === 'string') {
        return isMarkdownContent(value)
      } else if (Array.isArray(value)) {
        return value.some(item => checkObjectForMarkdown(item))
      } else if (typeof value === 'object' && value !== null) {
        return Object.values(value).some(item => checkObjectForMarkdown(item))
      }
      return false
    }

    if (checkObjectForMarkdown(obj)) {
      // 如果对象包含markdown内容，尝试提取并返回
      const extractMarkdownFromObject = (value: any): string | null => {
        if (typeof value === 'string' && isMarkdownContent(value)) {
          return value
        } else if (Array.isArray(value)) {
          for (const item of value) {
            const result = extractMarkdownFromObject(item)
            if (result) return result
          }
        } else if (typeof value === 'object' && value !== null) {
          for (const item of Object.values(value)) {
            const result = extractMarkdownFromObject(item)
            if (result) return result
          }
        }
        return null
      }

      const markdownContent = extractMarkdownFromObject(obj)
      if (markdownContent) {
        return markdownContent
      }
    }
  }

  try {
    return JSON.stringify(obj, null, 2)
  } catch (e) {
    return String(obj)
  }
}

// 检查内容是否为markdown格式
const isMarkdownContent = (content: any) => {
  if (typeof content !== 'string') return false

  // 如果内容太短，不太可能是markdown
  if (content.trim().length < 3) return false

  // 排除一些明显不是markdown的内容
  const excludePatterns = [
    /^[\d\s.,]+$/,          // 纯数字和标点
    /^[A-Z_]+$/,            // 纯大写字母（可能是常量）
    /^\w+\s*=\s*.+$/,       // 简单的赋值语句
    /^https?:\/\//,         // 纯URL
    /^[\w\s]+:\s*[\w\s]+$/, // 简单的键值对
  ]

  if (excludePatterns.some(pattern => pattern.test(content.trim()))) {
    return false
  }

  const markdownPatterns = [
    /^#{1,6}\s/m,           // 标题 # ## ###
    /\*\*.*?\*\*/,          // 粗体 **text**
    /\*.*?\*/,              // 斜体 *text*（但排除单独的星号）
    /`.*?`/,                // 行内代码 `code`
    /```[\s\S]*?```/,       // 代码块 ```code```
    /^\s*[-*+]\s/m,         // 无序列表 - * +
    /^\s*\d+\.\s/m,         // 有序列表 1. 2.
    /\[.*?\]\(.*?\)/,       // 链接 [text](url)
    /!\[.*?\]\(.*?\)/,      // 图片 ![alt](url)
    /^\s*>\s/m,             // 引用 >
    /^\s*\|.*\|/m,          // 表格 |col1|col2|
    /^\s*---+\s*$/m,        // 水平分割线 ---
    /~~.*?~~/,              // 删除线 ~~text~~
    /^\s*\d+\.\s+.+$/m,     // 有序列表项
    /^\s*[-*+]\s+.+$/m,     // 无序列表项
    /\n\s*\n/,              // 段落分隔（双换行）
  ]

  // 计算匹配的模式数量
  const matchCount = markdownPatterns.filter(pattern => pattern.test(content)).length

  // 如果匹配多个模式，更可能是markdown
  if (matchCount >= 2) return true

  // 如果只匹配一个模式，检查是否是强markdown指示符
  if (matchCount === 1) {
    const strongPatterns = [
      /^#{1,6}\s/m,           // 标题
      /```[\s\S]*?```/,       // 代码块
      /^\s*\|.*\|/m,          // 表格
      /^\s*---+\s*$/m,        // 水平分割线
    ]
    return strongPatterns.some(pattern => pattern.test(content))
  }

  return false
}

// 判断结果的渲染类型
const getResultRenderType = (result: any) => {
  if (!result) return 'json'

  const actionResult = result

  // 检查是否是新格式的表格数据
  if (actionResult.chartType === 'table' && actionResult.select_data && Array.isArray(actionResult.select_data)) {
    return 'table'
  }
  
  // 检查是否有表格数据 (tableType为"TABLE")
  if (actionResult.retrievedKnowledge?.tableSchemas) {
    const hasTableData = actionResult.retrievedKnowledge.tableSchemas.some(
      (schema: any) => schema.tableType === 'TABLE' && schema.columns && schema.columns.length > 0
    )
    if (hasTableData) {
      return 'table'
    }
  }
  if(actionResult.chartType == 'table') {
    return 'table'
  }
  // 检查是否有图表数据 (chartType为"line") actionResult.chartType === 'line' &&
  if ( actionResult.select_data) {
    return 'chart'
  }

  // 检查是否有SQL语句 (message字段包含SQL)
  if (actionResult.message && typeof actionResult.message === 'string' && actionResult.chartType !== 'line') {
    const message = actionResult.message.trim().toUpperCase()
    if (message.includes('SELECT') || message.includes('INSERT') || message.includes('UPDATE') || message.includes('DELETE')) {

      return 'sql'
    }
  }

  // 检查是否有markdown内容
  if (actionResult.message && typeof actionResult.message === 'string' && isMarkdownContent(actionResult.message)) {

    return 'markdown'
  }

  // 递归检查对象中的markdown内容
  const checkForMarkdownInObject = (obj: any): boolean => {
    if (typeof obj === 'string') {
      return isMarkdownContent(obj)
    } else if (Array.isArray(obj)) {
      return obj.some(item => checkForMarkdownInObject(item))
    } else if (typeof obj === 'object' && obj !== null) {
      return Object.values(obj).some(value => checkForMarkdownInObject(value))
    }
    return false
  }

  if (checkForMarkdownInObject(actionResult)) {
    return 'markdown'
  }

  return 'json'
}

// 获取表格数据
const getTableData = (result: any) => {
  
  // 处理新的数据格式
  if (result?.chartType === 'table' && result?.select_data && Array.isArray(result.select_data)) {
    // console.log('使用新的表格数据格式')
    return result.select_data
  }
  
  // 处理原有的数据格式
  if (!result?.retrievedKnowledge?.tableSchemas) return []

  const tableSchema = result.retrievedKnowledge.tableSchemas.find(
    (schema: any) => schema.tableType === 'TABLE' && schema.columns && schema.columns.length > 0
  )

  return tableSchema?.columns || []
}

// 获取表格列
const getTableColumns = (result: any) => {
  const data = getTableData(result)
  if (!data || data.length === 0) return []

  // 从第一行数据中提取列名
  return Object.keys(data[0])
}

// 获取图表表格列（用于summary区域的图表数据）
const getChartTableColumns = (data: any[]) => {
  if (!data || data.length === 0) return []

  // 从第一行数据中提取列名
  return Object.keys(data[0])
}

// 生成基础图表配置（公共部分）
const createBaseChartOption = (title: string, customGrid?: any) => {
  return {
    title: {
      text: title || '',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 600,
        color: '#1f2937',
        fontFamily: 'Helvetica Neue,Helvetica,"PingFang SC","Hiragino Sans GB","Microsoft YaHei","微软雅黑",Arial,sans-serif'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
      ...customGrid
    },
    tooltip: {
      trigger: 'axis'
    }
  }
}

// 生成折线图/柱状图的轴配置（单个系列）
const createAxisChartConfig = (baseOption: any, xData: any[], yData: any[], chartType: string, seriesName: string) => {
  return {
    ...baseOption,
    xAxis: {
      type: 'category',
      data: xData,
      axisLabel: {
        rotate: xData.some(label => String(label).length > 6) ? 45 : 0
      }
    },
    yAxis: {
      type: 'value'
    },
    series: [{
      name: seriesName || 'Value',
      type: chartType === 'line' ? 'line' : 'bar',
      data: yData,
      smooth: chartType === 'line',
      itemStyle: {
        color: '#3b82f6',
        borderRadius: chartType === 'bar' ? [4, 4, 0, 0] : 0
      },
      barMinHeight: 10,
      barWidth: '10%'
    }]
  }
}

// 生成多系列折线图/柱状图的轴配置
const createMultiSeriesAxisChartConfig = (baseOption: any, xData: any[], data: any[], dataFields: string[], chartType: string) => {
  // 预定义颜色数组
  const colors = [
    '#3b82f6', '#ef4444', '#10b981', '#f59e0b', '#8b5cf6',
    '#06b6d4', '#84cc16', '#f97316', '#ec4899', '#6366f1'
  ]

  // 为每个数据字段创建一个系列
  const series = dataFields.map((field, index) => {
    const yData = data.map(item => {
      const value = item[field]
      return typeof value === 'string' ? parseFloat(value) || 0 : value || 0
    })

    return {
      name: field,
      type: chartType === 'line' ? 'line' : 'bar',
      data: yData,
      smooth: chartType === 'line',
      itemStyle: {
        color: colors[index % colors.length],
        borderRadius: chartType === 'bar' ? [4, 4, 0, 0] : 0
      },
      barWidth: chartType === 'bar' ? '10%' : undefined
    }
  })

  return {
    ...baseOption,
    xAxis: {
      type: 'category',
      data: xData,
      axisLabel: {
        rotate: xData.some(label => String(label).length > 6) ? 45 : 0
      }
    },
    yAxis: {
      type: 'value'
    },
    legend: {
      data: dataFields,
      top: '8%',
      left: 'center'
    },
    series: series
  }
}

// 生成多系列饼图配置（支持多个饼图或单个饼图多数据字段）
const createMultiSeriesPieChartConfig = (baseOption: any, data: any[], xField: string, dataFields: string[]) => {
  // 预定义颜色数组
  const colors = [
    '#3b82f6', '#ef4444', '#10b981', '#f59e0b', '#8b5cf6',
    '#06b6d4', '#84cc16', '#f97316', '#ec4899', '#6366f1'
  ]

  if (dataFields.length === 1) {
    // 单个数据字段的饼图
    const valueField = dataFields[0]
    const pieData = data.map(item => ({
      name: item[xField] || '',
      value: typeof item[valueField] === 'string' ? parseFloat(item[valueField]) || 0 : item[valueField] || 0
    }))

    return {
      ...baseOption,
      series: [{
        name: valueField,
        type: 'pie',
        radius: '50%',
        center: ['50%', '60%'],
        data: pieData,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        },
        label: {
          show: true,
          formatter: '{b}: {d}%'
        }
      }]
    }
  } else {
    // 多个数据字段：创建多个同心圆饼图
    const series = dataFields.map((field, index) => {
      const pieData = data.map(item => ({
        name: `${item[xField]}-${field}`,
        value: typeof item[field] === 'string' ? parseFloat(item[field]) || 0 : item[field] || 0
      }))

      const radiusInner = index * 15 + 20
      const radiusOuter = radiusInner + 15

      return {
        name: field,
        type: 'pie',
        radius: [`${radiusInner}%`, `${radiusOuter}%`],
        center: ['50%', '60%'],
        data: pieData,
        label: {
          show: index === dataFields.length - 1, // 只在最外层显示标签
          formatter: '{b}: {d}%'
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    })

    return {
      ...baseOption,
      legend: {
        data: dataFields,
        top: '8%',
        left: 'center'
      },
      series: series
    }
  }
}

// 异步获取_chart工具的详细数据
const fetchChartDataFromTool = async (toolName: string, stepId: string, agentName: string) => {
  try {
    console.log('📊 获取_chart工具详细数据:', { toolName, stepId })

    const toolDetail = await AxiosApiService.getToolDetail(stepId)

    if (toolDetail && toolDetail.actionDetails?.actionResult) {
      // 解析工具结果
      let toolResult = null
      try {
        const resultData = toolDetail.actionDetails.actionResult
        if (typeof resultData === 'string') {
          toolResult = JSON.parse(resultData)
        } else if (typeof resultData === 'object') {
          toolResult = resultData
        }
      } catch (error) {
        console.error('❌ 解析_chart工具结果失败:', error)
        return null
      }

      if (toolResult) {
        // 验证数据有效性
        const chartData = toolResult.select_data || toolResult.data || []
        if (Array.isArray(chartData) && chartData.length > 0) {
          // 构建返回对象，优先使用series.dataFields
          const chartItem = {
            toolName: toolName,
            stepId: stepId,
            chartType: toolResult.chartType || 'line',
            data: chartData,
            xFields: toolResult.XFields || toolResult.xFields,
            title: toolDetail.actionDetails.toolParameters.chartTitle,
            agentName: agentName
          }

          // 优先使用series.dataFields，如果没有则从YFields转换
          if (toolResult.series && toolResult.series.dataFields && Array.isArray(toolResult.series.dataFields)) {
            chartItem.series = toolResult.series
          } else if (toolResult.YFields || toolResult.yFields) {
            // 兼容性转换：将YFields转换为series.dataFields格式
            const yFieldsValue = toolResult.YFields || toolResult.yFields
            const dataFields = Array.isArray(yFieldsValue) ? yFieldsValue : [yFieldsValue]
            chartItem.series = {
              dataFields: dataFields
            }
            // 保留原有字段用于向后兼容
            chartItem.yFields = yFieldsValue
          } else {
            // 如果都没有，使用默认值
            chartItem.series = {
              dataFields: ['value']
            }
          }

          return chartItem
        } else {
          console.warn('⚠️ _chart工具数据为空或格式不正确:', toolResult)
        }
      }
    } else {
      console.warn('⚠️ 未获取到工具详情或执行结果:', stepId)
    }
  } catch (error) {
    console.error('❌ 获取_chart工具详情失败:', error)
  }

  return null
}

// 渲染summary区域的图表
/**
 * 渲染摘要图表
 * @param {any} chartItem - 图表数据项
 * @param {string} containerId - 容器ID
 */
const renderSummaryChart = (chartItem: any, containerId: string) => {
  nextTick(() => {
    const container = document.getElementById(containerId)
    // console.log('🎨 renderSummaryChart 调用:', {
    //   containerId,
    //   containerFound: !!container,
    //   chartType: chartItem.chartType,
    //   dataLength: chartItem.data?.length || 0
    // })

    if (!container) {
      console.warn('⚠️ 未找到图表容器:', containerId)
      return
    }

    if (!chartItem.data || chartItem.data.length === 0) {
      console.warn('⚠️ 图表数据为空:', chartItem)
      return
    }

    try {
      // 销毁现有图表实例
      if (chartInstances.value.has(containerId)) {
        const existingChart = chartInstances.value.get(containerId)
        if (existingChart) {
          existingChart.dispose()
        }
        chartInstances.value.delete(containerId)
        // 清理渲染标记
        renderedCharts.value.delete(containerId)
      }

      // 创建ECharts实例
      const chart = echarts.init(container,null, {devicePixelRatio: 2.5})
      chartInstances.value.set(containerId, chart)

      // 生成图表配置
      const option = generateSummaryChartOption(chartItem)

      // 设置图表配置
      if (option) {
        chart.setOption(option, true)
      }

      // 监听窗口大小变化
      const resizeHandler = () => {
        chart.resize()
      }

      window.addEventListener('resize', resizeHandler)
      ;(chart as any)._resizeHandler = resizeHandler

    } catch (error) {
      console.error('❌ 渲染summary图表失败:', error)
      // 出错时也要清理渲染标记
      renderedCharts.value.delete(containerId)
    }
  })
}

// 生成summary图表配置
const generateSummaryChartOption = (chartItem: any) => {
  const { data, chartType, xFields, yFields, title, series } = chartItem
  if (!data || data.length === 0) {
    return null
  }

  // 统一获取数据字段配置，优先使用series.dataFields，废弃yFields
  let dataFields = []
  if (series && series.dataFields && Array.isArray(series.dataFields)) {
    dataFields = series.dataFields
  } else if (yFields) {
    // 兼容性支持：将yFields转换为dataFields格式
    console.warn('⚠️ yFields已废弃，请使用series.dataFields')
    dataFields = Array.isArray(yFields) ? yFields : [yFields]
  } else {
    dataFields = ['value'] // 默认字段
  }

  // 饼图配置 - 支持多个数据字段
  if (chartType === 'pie') {
    const baseOption = {
      title: {
        text: title || '',
        left: 'center',
        textStyle: {
        fontSize: 16,
        fontWeight: 600,
        color: '#1f2937',
        fontFamily: '"Helvetica Neue",Helvetica,"PingFang SC","Hiragino Sans GB","Microsoft YaHei","微软雅黑",Arial,sans-serif'
      }
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      }
    }

    return createMultiSeriesPieChartConfig(baseOption, data, xFields, dataFields)
  }

  // 折线图和柱状图配置 - 支持多个Y字段
  const xData = data.map(item => item[xFields] || '')

  const baseOption = createBaseChartOption(title)
  return createMultiSeriesAxisChartConfig(baseOption, xData, data, dataFields, chartType)
}

// 获取SQL消息
const getSqlMessage = (result: any) => {
  return result?.message || ''
}

// 渲染markdown内容
const renderMarkdown = (content: string) => {
  if (!content || typeof content !== 'string') return ''

  try {
    // 配置marked选项
    
    const marked = new Marked(
      markedHighlight({
        emptyLangClass: 'hljs',
        langPrefix: 'hljs language-',
        highlight(code, lang, info) {
          const language = hljs.getLanguage(lang) ? lang : 'plaintext';
          return hljs.highlight(code, { language }).value;
        }
      })
    );
    marked.setOptions({
      breaks: true,        // 支持换行符转换为<br>
      gfm: true,          // 启用GitHub风格的markdown
      sanitize: false,    // 不进行HTML清理（因为我们使用vue-dompurify-html）
      smartLists: true,   // 智能列表
      smartypants: true   // 智能标点符号
    })
    return marked.parse(content)
  } catch (error) {
    console.error('Markdown渲染失败:', error)
    return content // 如果渲染失败，返回原始内容
  }
}

// 从结果中提取markdown内容
const getMarkdownContent = (result: any) => {
  if (!result) return ''

  // 如果result本身就是字符串且是markdown
  if (typeof result === 'string' && isMarkdownContent(result)) {
    return result
  }

  // 递归搜索对象中的markdown内容
  const extractMarkdownFromObject = (obj: any): string => {
    if (typeof obj === 'string' && isMarkdownContent(obj)) {
      return obj
    } else if (Array.isArray(obj)) {
      for (const item of obj) {
        const content = extractMarkdownFromObject(item)
        if (content) return content
      }
    } else if (typeof obj === 'object' && obj !== null) {
      // 优先检查常见的字段名
      const priorityFields = ['message', 'content', 'text', 'description', 'body']
      for (const field of priorityFields) {
        if (obj[field] && typeof obj[field] === 'string' && isMarkdownContent(obj[field])) {
          return obj[field]
        }
      }

      // 如果优先字段没有找到，检查所有字段
      for (const value of Object.values(obj)) {
        const content = extractMarkdownFromObject(value)
        if (content) return content
      }
    }
    return ''
  }

  return extractMarkdownFromObject(result)
}

// 生成图表配置的工具函数
const generateChartOption = (chartData: any[], chartType: string, result: any) => {
  const title = selectedTool.value?.parameters?.chartTitle || ''

  // 统一获取数据字段配置，优先使用series.dataFields，废弃YFields
  let dataFields = []
  if (result.series && result.series.dataFields && Array.isArray(result.series.dataFields)) {
    dataFields = result.series.dataFields
  } else if (result.YFields) {
    // 兼容性支持：将YFields转换为dataFields格式
    console.warn('⚠️ YFields已废弃，请使用series.dataFields')
    dataFields = Array.isArray(result.YFields) ? result.YFields : [result.YFields]
  } else {
    dataFields = ['value'] // 默认字段
  }

  // 饼图配置 - 支持多个数据字段
  if (chartType === 'pie') {
    const baseOption = {
      title: {
        text: title,
        left: 'center',
        textStyle: {
          fontSize: 16,
          fontWeight: 600,
          color: '#1f2937',
          fontFamily: 'Helvetica Neue,Helvetica,"PingFang SC","Hiragino Sans GB","Microsoft YaHei","微软雅黑",Arial,sans-serif'
        }
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      }
    }

    return createMultiSeriesPieChartConfig(baseOption, chartData, result.XFields, dataFields)
  }

  // 折线图和柱状图配置 - 支持多个Y字段
  if (chartType === 'line' || chartType === 'bar') {
    const xData = chartData.map(item => item[result.XFields] || '');

    const customGrid = {
      top: dataFields.length > 1 ? '15%' : '10%'  // 多系列时需要更多空间给图例
    }
    const baseOption = createBaseChartOption(title, customGrid)

    // 使用多系列配置函数
    const actualChartType = chartType === 'auto' ? 'line' : chartType
    const customOption = createMultiSeriesAxisChartConfig(baseOption, xData, chartData, dataFields, actualChartType)
    customOption.tooltip = {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    }

    return customOption;
  }

  return null;
}

// 渲染工具结果中的图表
const renderToolChart = (result: any, containerId: string) => {
  if (!result?.select_data || !result.chartType) return

  const container = document.getElementById(containerId)
  if (!container) return

  const chartData = result.select_data
  const chartType = result.chartType

  // 清理已存在的图表实例
  if (chartInstances.value.has(containerId)) {
    const existingChart = chartInstances.value.get(containerId)
    if (existingChart && (existingChart as any)._resizeHandler) {
      window.removeEventListener('resize', (existingChart as any)._resizeHandler)
    }
    existingChart?.dispose()
    chartInstances.value.delete(containerId)
  }

  // 创建ECharts实例
  const chart = echarts.init(container,null, {devicePixelRatio: 2.5})
  chartInstances.value.set(containerId, chart)

  // 生成图表配置
  const option = generateChartOption(chartData, chartType, result)

  // 设置图表配置
  if (option) {
    chart.setOption(option, true) // true表示不合并，完全替换
  }

  // 监听窗口大小变化
  const resizeHandler = () => {
    chart.resize()
  }

  window.addEventListener('resize', resizeHandler)

  // 存储清理函数到chart实例上，便于后续清理
  ;(chart as any)._resizeHandler = resizeHandler
}


const handleInputKeydown = (event) => {
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault()
    sendMessage()
  }
}

// 处理按钮点击事件
const handleButtonClick = (event) => {
  event.preventDefault()
  console.log('🔘 按钮被点击，当前sending状态:', sending.value)

  if (sending.value) {
    console.log('🛑 执行停止操作')
    stopMessage()
  } else {
    console.log('📤 执行发送操作')
    sendMessage()
  }
}

const adjustTextareaHeight = () => {
  nextTick(() => {
    if (messageInputRef.value) {
      messageInputRef.value.style.height = 'auto'
      messageInputRef.value.style.height = Math.min(messageInputRef.value.scrollHeight, 120) + 'px'
    }
  })
}

const sendMessage = async () => {
  if (!messageInput.value.trim() || sending.value) return

  // 校验数据源是否选择
  if (!dataSourceSelection.value.dataSourceId) {
    ElMessage.warning('请选择数据源')
    return
  }

  const userMessage = messageInput.value.trim()
  messageInput.value = ''
  sending.value = true

  try {
    // 添加用户消息到对话轮次
    const roundId = Date.now().toString()
    const currentRound = {
      id: roundId,
      userMessage,
      timestamp: new Date(),
      planData: null, // 将在AI回复时填充
      agentData: [],
      thinkData: [],
      toolData: [],
      executionSteps: [], // 新的执行步骤数组
      planSummary: ''
    }
    conversationRounds.value.push(currentRound)

    // 滚动到底部
    scrollToBottom()

    // 构建工具上下文
    const toolContext = dataSourceSelection.value.dataSourceId ?
      DataSourceApiService.buildToolContext(
        dataSourceSelection.value.dataSourceId,
        dataSourceSelection.value.databaseName,
        dataSourceSelection.value.schemaName
      ) : {}

    // 构建流式请求
    const streamingRequest: DirectStreamingRequest = {
      chatId: currentChatId.value,
      // userId: currentUserId.value,
      message: userMessage,
      toolContext: toolContext,
      modelId: selectedModelId.value || undefined
    }

    // 创建AbortController用于取消请求
    currentAbortController.value = new AbortController()

    // 调用新的流式API
    await DirectApiService.sendMessageWithStreaming(
    // await AxiosApiService.sendMessageWithStreamingDirect(
      newChatSettings.value.planTemplateId,
      streamingRequest,
      async (event: DirectStreamEvent) => await handleStreamEvent(event, currentRound),
      (error: Error) => {
        console.error('❌ 流式请求错误:', error)

        // 如果是网络连接错误，提供友好的提示
        if (error.message.includes('Failed to fetch') || error.message.includes('ECONNREFUSED')) {

        } else {
          // alert('发送消息失败: ' + error.message)
        }

        // 错误时也要重置发送状态
        sending.value = false
        currentAbortController.value = null
        currentPlanId.value = null
      },
      () => {
        // onComplete 回调：流式响应完成时执行
        sending.value = false
        currentAbortController.value = null
        currentPlanId.value = null
        // 消息发送完成后自动刷新历史对话
        // console.log('🔄 消息发送完成，自动刷新历史对话')
        refreshHistory(historySearchKeyword.value)
      },
      5 * 60 * 1000, // 超时时间
      currentAbortController.value // 传入AbortController
    )

  } catch (error) {
    console.error('发送消息失败:', error)
    sending.value = false
    currentAbortController.value = null
    currentPlanId.value = null
  }
}

// 停止消息发送
const stopMessage = async () => {
  if (!sending.value) return

  try {
    console.log('🛑 用户请求停止消息发送')

    // 1. 取消流式请求
    if (currentAbortController.value) {
      currentAbortController.value.abort()
      console.log('✅ 已取消流式请求')
    }

    // 2. 如果有planId，调用停止执行接口
    if (currentPlanId.value) {
      try {
        // 直接调用停止执行接口
        const response = await fetch(`/api/executor/stop/${currentPlanId.value}`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({})
        })

        if (response.ok) {
          console.log('✅ 已调用停止执行接口')
        } else {
          console.warn('⚠️ 停止执行接口返回错误:', response.status)
        }
      } catch (stopError) {
        console.warn('⚠️ 停止执行接口调用失败:', stopError)
        // 即使停止接口失败，也继续重置状态
      }
    }

    // 3. 重置状态
    sending.value = false
    currentAbortController.value = null
    currentPlanId.value = null

    console.log('✅ 消息发送已停止')
  } catch (error) {
    console.error('❌ 停止消息发送失败:', error)
    // 即使出错也要重置状态
    sending.value = false
    currentAbortController.value = null
    currentPlanId.value = null
  }
}

// 流式事件处理函数
const handleStreamEvent = async (event: DirectStreamEvent, currentRound: any) => {
  // 新的/direct端点直接返回StreamingExecutionResponse对象
  // 检查是否是直接的StreamingExecutionResponse格式
  if (event && typeof event === 'object' && !event.type) {
    // 直接处理StreamingExecutionResponse
    await handleDirectExecutionResponse(event, currentRound)
    return
  }
}

// 新增：直接处理StreamingExecutionResponse对象
const handleDirectExecutionResponse = async (response: any, currentRound: any) => {
  try {
    // 初始化执行步骤数组
    if (!currentRound.executionSteps) {
      currentRound.executionSteps = []
    }

    // 更新计划信息（从扁平化字段中获取）
    if (response.planId) {
      // 设置当前planId，用于停止功能
      if (!currentPlanId.value) {
        currentPlanId.value = response.planId
      }

      currentRound.planData = {
        id: response.planId,
        chatId: response.chatId,
        title: response.planTitle || '计划执行中...',
        userRequest: response.userRequest,
        completed: response.agentCompleted,
        summary: response.planSummary,
        // status: response.planCompleted ? 'completed' : 'in-progress'
        status: response.agentStatus
      }
    }

    // 更新Agent信息（从扁平化字段中获取）
    if (response.agentExecutionId) {
      if (!currentRound.agentData) {
        currentRound.agentData = []
      }

      const agentId = response.agentExecutionId.toString()
      let agent = currentRound.agentData.find(a => a.id === agentId)

      if (!agent) {
        agent = {
          id: agentId,
          agentName: response.agentName,
          agentDescription: response.agentDescription,
          status: response.agentStatus,
          currentStep: response.agentCurrentStep,
          maxSteps: response.agentMaxSteps,
          completed: response.agentCompleted
        }
        currentRound.agentData.push(agent)
      } else {
        // 更新现有Agent信息
        Object.assign(agent, {
          agentName: response.agentName,
          agentDescription: response.agentDescription,
          status: response.agentStatus,
          currentStep: response.agentCurrentStep,
          maxSteps: response.agentMaxSteps,
          completed: response.agentCompleted
        })
      }
    }
    // 检测并收集_chart工具的结果数据
        if (response.toolName && response.toolName.endsWith('_chart') && (response.status === 'FISISHED' || response.status === 'FINISHED')) {
          // 初始化chartData数组如果不存在
          if (!currentRound.chartData) {
            currentRound.chartData = []
          }

          // 使用stepId调用getToolDetail接口获取详细数据
          const stepId = response.id?.toString()
          if (stepId) {
            const chartData = await fetchChartDataFromTool(response.toolName, stepId, response.agentName)
            if (chartData) {
              currentRound.chartData.push(chartData)
              console.log('📊 成功收集到_chart工具数据:', {
                toolName: response.toolName,
                chartType: chartData.chartType,
                dataLength: chartData.data.length
              })
            }
          }
        }
    

    // 处理SUMMARY_AGENT的特殊情况：累积thinkOutput并提取planSummary字段
    if (response.agentExecutionId == "0" && response.agentName === 'SUMMARY_AGENT') {
      // 初始化summaryContent如果不存在
      if (!currentRound.summaryContent) {
        currentRound.summaryContent = ''
      }

      // 累积thinkOutput内容
      if (response.thinkOutput) {
        currentRound.summaryContent += response.thinkOutput
      }

      // 如果有planSummary字段，表示总结完成
      if (response.planSummary !== undefined) {
        // 使用累积的内容作为最终的planSummary，如果planSummary为空的话
        currentRound.planSummary = response.planSummary || currentRound.summaryContent

        // 清理临时的summaryContent
        delete currentRound.summaryContent
        
        // 强制触发Vue响应式更新
        triggerRef(conversationRounds)
        nextTick(async () => {
          scrollToBottom()
          // 刷新助手列表
          sending.value = false
          // 消息发送完成后自动刷新历史对话
          refreshHistory(historySearchKeyword.value)
          
        })
      } else {
        // 如果没有planSummary字段，只是中间片段，强制触发Vue响应式更新以显示累积内容
        triggerRef(conversationRounds)
        scrollToBottom()
      }
      
      return // 不添加到executionSteps中
    }

    // 过滤掉不需要显示的步骤（如PLAN_COORDINATOR的状态更新和纯状态更新）
    if (response.agentName === 'PLAN_COORDINATOR' && !response.thinkOutput && !response.actionNeeded) {
      return
    }

    // 过滤掉纯Agent状态更新（没有思考内容和工具调用的状态更新）
    // if (!response.actionNeeded && !response.toolName) {
    //   console.log('🚫 跳过纯Agent状态更新:', response)
    //   return
    // }

    // 创建执行步骤记录（基于ThinkActRecord）
    const executionStep = {
      id: response.id?.toString() || Date.now().toString(),
      thinkOutput: response.thinkOutput,
      actionNeeded: response.actionNeeded,
      toolName: response.toolName,
      status: response.status,
      thinkStartTime: response.thinkStartTime,
      actEndTime: response.actEndTime,
      agentExecutionId: response.agentExecutionId,
      agentName: response.agentName,
      agentDescription: response.agentDescription,
      agentStatus: response.agentStatus,
      agentCompleted: response.agentCompleted,
      hasDetails: true, // 总是有详细信息可查询
      timestamp: new Date()
    }

    // 如果有工具调用，添加工具信息
    if (response.actionNeeded && response.toolInfo) {
      executionStep.toolInfo = response.toolInfo
    }

    // 如果有错误信息，添加错误信息
    if (response.errorInfo) {
      executionStep.errorInfo = response.errorInfo
    }

    // 检查是否已存在相同ID的步骤，如果存在则更新，否则添加
    const existingStepIndex = currentRound.executionSteps.findIndex(s => s.id === executionStep.id)
    if (existingStepIndex >= 0) {
      // 更新现有步骤
      currentRound.executionSteps[existingStepIndex] = executionStep
    } else {
      // 添加新步骤
      currentRound.executionSteps.push(executionStep)
    }


    // 🔧 关键修复：在数据接收时就更新Agent状态
    updateAgentStatusInExecutionSteps(currentRound)

    // 强制触发Vue响应式更新
    triggerRef(conversationRounds)
    nextTick(() => {
      scrollToBottom()
      sending.value = false
    })

  } catch (error) {
    console.error('❌ 处理直接执行响应失败:', error)
  }
}

const scrollToBottom = () => {
  nextTick(() => {
    if (chatContainerRef.value) {
      chatContainerRef.value.scrollTop = chatContainerRef.value.scrollHeight
    }
  })
}

const formatTime = (date) => {
  if (!date) return ''
  const now = dayjs()
  const target = dayjs(date)

  // 如果是今天，显示时间
  if (target.isSame(now, 'day')) {
    return target.format('HH:mm')
  }
  // 如果是昨天，显示"昨天 HH:mm"
  else if (target.isSame(now.subtract(1, 'day'), 'day')) {
    return '昨天 ' + target.format('HH:mm')
  }
  // 如果是今年，显示"MM-DD HH:mm"
  else if (target.isSame(now, 'year')) {
    return target.format('MM-DD HH:mm')
  }
  // 其他情况显示完整日期
  else {
    return target.format('YYYY-MM-DD HH:mm')
  }
}

// 安全的JSON解析函数
const safeJsonParse = (jsonString) => {
  if (!jsonString || typeof jsonString !== 'string') {
    return null
  }

  try {
    return JSON.parse(jsonString)
  } catch (error) {
    console.warn('JSON解析失败:', jsonString, error)
    return null
  }
}

// 获取SQL查询的图表数据（支持流式和历史数据）
const getSqlQueryChartData = (round) => {
  if (!round) {
    return null
  }

  let sqlQueryData = null

  // 方式1: 从executionSteps中查找（历史数据格式）
  if (round.executionSteps && round.executionSteps.length > 0) {
    const sqlQueryStep = round.executionSteps.find(step =>
      step.toolName === 'sql_query' && step.toolInfo && step.toolInfo.result
    )

    if (sqlQueryStep) {
      const result = sqlQueryStep.toolInfo.result
      if (result && (result.chart_config || (result.data && result.data.length > 0))) {
        sqlQueryData = {
          chartConfig: result.chart_config || null,
          data: result.data || [],
          columns: result.columns || [],
          sql: result.sql,
          rowCount: result.rowCount,
          duration: result.duration
        }
      }
    }
  }

  // 方式2: 从toolData中查找（流式数据格式）
  if (!sqlQueryData && round.toolData && round.toolData.length > 0) {
    const sqlQueryTool = round.toolData.find(tool =>
      tool.toolName === 'sql_query' && tool.sqlQueryData
    )

    if (sqlQueryTool && sqlQueryTool.sqlQueryData) {
      const data = sqlQueryTool.sqlQueryData
      if (data.chart_config || (data.data && data.data.length > 0)) {
        sqlQueryData = {
          chartConfig: data.chart_config || null,
          data: data.data || [],
          columns: data.columns || [],
          sql: data.sql,
          rowCount: data.rowCount,
          duration: data.duration
        }
      }
    }
  }

  // 方式3: 直接从round中查找（其他可能的数据格式）
  if (!sqlQueryData && round.sqlQueryData) {
    const data = round.sqlQueryData
    if (data.chart_config || (data.data && data.data.length > 0)) {
      sqlQueryData = {
        chartConfig: data.chart_config || null,
        data: data.data || [],
        columns: data.columns || [],
        sql: data.sql,
        rowCount: data.rowCount,
        duration: data.duration
      }
    }
  }

  return sqlQueryData
}

// 渲染图表
const renderChart = (chartConfig, containerId) => {
  try {
    console.log('📈 开始渲染图表:', chartConfig, '容器ID:', containerId)

    const chartContainer = document.getElementById(containerId)
    if (!chartContainer) {
      console.error('❌ 未找到图表容器:', containerId)
      return
    }

    // 初始化ECharts
    const chart = echarts.init(chartContainer,null, {devicePixelRatio: 2.5})

    // 设置图表配置
    const option = {
      ...chartConfig,
      animation: true,
      animationDuration: 1000,
      animationEasing: 'cubicOut'
    }

    // 渲染图表
    chart.setOption(option)

    // 响应式处理
    const resizeObserver = new ResizeObserver(() => {
      chart.resize()
    })
    resizeObserver.observe(chartContainer)

    // 存储图表实例以便后续操作
    chartContainer._chartInstance = chart
    chartContainer._resizeObserver = resizeObserver

  } catch (error) {
    console.error('❌ 图表渲染失败:', error)
  }
}

// 渲染历史消息 - 使用流式格式
const renderHistoryMessages = async (data) => {

  try {
    let executionRecords = []

    // 兼容不同的数据格式
    if (data.executions && Array.isArray(data.executions)) {
      executionRecords = data.executions
    } else if (data.history && Array.isArray(data.history)) {
      executionRecords = data.history
    } else {
      console.warn('⚠️ 未找到有效的历史数据')
      return
    }

    // 渲染每个历史记录
    for (const [index, record] of executionRecords.entries()) {
      try {
        await renderHistoryRecordAsStream(record)
      } catch (error) {
        console.error(`❌ 第${index + 1}条记录渲染失败:`, error)
      }
    }

    // 滚动到底部
    nextTick(() => {
      scrollToBottom()
    })
  } catch (error) {
    console.error('❌ 渲染历史消息失败:', error)
  }
}

// 渲染单个历史记录 - 使用流式格式
const renderHistoryRecordAsStream = async (record) => {
  // 创建对话轮次
  const roundId = 'history-' + (record.planId || record.chatId || Date.now())
  const currentRound = {
    id: roundId,
    userMessage: record.userRequest || '历史消息',
    timestamp: new Date(record.startTime || Date.now()),
    executionSteps: [],
    isHistory: true,
    planSummary: record.planSummary || record.summary,
    chartData: [] // 初始化图表数据数组
  }

  // 处理智能体执行序列，转换为流式格式
  if (record.agentExecutionSequence && record.agentExecutionSequence.length > 0) {
    for (const agent of record.agentExecutionSequence) {
      // 智能状态修正：如果智能体有完成的步骤，则认为智能体已完成
      const hasCompletedSteps = agent.thinkActSteps && agent.thinkActSteps.some(step =>
        step.status === 'FINISHED' && step.toolName === 'terminate'
      )
      const correctedAgentStatus = hasCompletedSteps ? 'completed' : (agent.status || 'completed')
      const correctedAgentCompleted = hasCompletedSteps ? true : (agent.completed !== false)

      // 处理每个智能体的思考行动步骤
      if (agent.thinkActSteps && agent.thinkActSteps.length > 0) {
        for (const thinkAct of agent.thinkActSteps) {
          // 创建流式执行步骤
          const executionStep = {
            id: thinkAct.id,
            agentExecutionId: agent.id,
            agentName: agent.agentName,
            agentDescription: agent.agentDescription,
            agentStatus: correctedAgentStatus,
            agentCompleted: correctedAgentCompleted,
            thinkOutput: thinkAct.thinkOutput || '',
            actionNeeded: !!thinkAct.toolName,
            toolName: thinkAct.toolName || null,
            status: thinkAct.status || 'FINISHED',
            thinkStartTime: thinkAct.thinkStartTime,
            thinkEndTime: thinkAct.thinkEndTime,
            actStartTime: thinkAct.actStartTime,
            actEndTime: thinkAct.actEndTime,
            errorMessage: thinkAct.errorMessage,
            timestamp: new Date(thinkAct.thinkStartTime || Date.now()),
            toolInfo: thinkAct.toolName ? {
              parameters: safeJsonParse(thinkAct.toolParameters),
              result: safeJsonParse(thinkAct.actionResult)
            } : null,
            errorInfo: thinkAct.errorMessage,
            isHistory: true,

          }

          // 检测并收集_chart工具的结果数据（历史数据）
          if (thinkAct.toolName && thinkAct.toolName.endsWith('_chart')) {
            const chartData = await fetchChartDataFromTool(thinkAct.toolName, thinkAct.id, agent.agentName)
            if (chartData) {
              currentRound.chartData.push(chartData)
              console.log('📊 收集到历史_chart工具数据:', {
                toolName: thinkAct.toolName,
                chartType: chartData.chartType,
                dataLength: chartData.data.length
              })
            }
          }

          currentRound.executionSteps.push(executionStep)
        }
      }
      if(agent.status == 'failed') {
        const executionStep = {
            id: agent.id,
            agentExecutionId: agent.id,
            agentName: agent.agentName,
            agentDescription: agent.agentDescription,
            agentStatus: agent.status || 'failed',
            status: agent.status || 'failed',
            isHistory: true,

          }
         currentRound.executionSteps.push(executionStep)
      }
    }
  }

  // 🔧 关键修复：处理历史数据时也更新Agent状态
  updateAgentStatusInExecutionSteps(currentRound)

  // 添加到对话轮次
  conversationRounds.value.push(currentRound)
}

// 🔧 关键修复：直接更新conversationRounds中的Agent状态
const updateAgentStatusInExecutionSteps = (currentRound) => {
  if (!currentRound.executionSteps || currentRound.executionSteps.length === 0) {
    return
  }

  // console.log('🔧 [updateAgentStatusInExecutionSteps] 开始更新原始数据中的Agent状态')

  // 按agentId分组，追踪每个Agent的状态变化
  const agentGroups = new Map()

  // 第一遍：收集所有Agent的步骤
  currentRound.executionSteps.forEach((step, index) => {
    const agentId = step.agentExecutionId

    if (!agentGroups.has(agentId)) {
      agentGroups.set(agentId, {
        agentId,
        steps: [],
        latestStatus: null,
        latestCompleted: null
      })
    }

    agentGroups.get(agentId).steps.push({ step, index })
  })

  // 第二遍：为每个Agent推断最终状态并更新所有相关步骤
  agentGroups.forEach((group, agentId) => {
    const { steps } = group

    // 🔧 修复：正确的状态收集逻辑 - 总是使用最新的状态
    let finalStatus = null
    let finalCompleted = null

    // 🔍 调试：打印所有步骤的状态
    // console.log(`🔍 [updateAgentStatusInExecutionSteps] Agent ${agentId} 的所有步骤状态:`,
    //   steps.map(({ step, index }) => ({
    //     index,
    //     id: step.id,
    //     agentStatus: step.agentStatus,
    //     agentCompleted: step.agentCompleted,
    //     status: step.status,
    //     toolName: step.toolName
    //   }))
    // )

    // 从后往前查找最新的有效状态（最新的状态应该覆盖之前的状态）
    for (let i = steps.length - 1; i >= 0; i--) {
      const { step } = steps[i]

      // 🔧 修复：总是使用最新的agentStatus，即使是null或undefined
      if (finalStatus === null && step.agentStatus !== undefined) {
        finalStatus = step.agentStatus
        // console.log(`🔍 [updateAgentStatusInExecutionSteps] Agent ${agentId} 使用步骤 ${i} 的agentStatus: ${step.agentStatus}`)
      }

      // 总是使用最新的agentCompleted状态
      if (finalCompleted === null && step.agentCompleted !== undefined) {
        finalCompleted = step.agentCompleted
        // console.log(`🔍 [updateAgentStatusInExecutionSteps] Agent ${agentId} 使用步骤 ${i} 的agentCompleted: ${step.agentCompleted}`)
      }

      // 如果已经找到了最新的状态和完成标记，就停止查找
      if (finalStatus !== null && finalCompleted !== null) {
        break
      }
    }

    // 如果没有找到明确的agentStatus，尝试从最新的step.status推断
    if (finalStatus === null) {
      for (let i = steps.length - 1; i >= 0; i--) {
        const { step } = steps[i]
        if (step.status) {
          finalStatus = mapStepStatusToAgentStatus(step.status)
          // console.log(`🔍 [updateAgentStatusInExecutionSteps] Agent ${agentId} 从step.status推断: ${step.status} -> ${finalStatus}`)
          break
        }
      }
    }

    // 🔧 修复：优先使用流式数据中的明确状态，只在必要时才推断
    let finalAgentStatus = finalStatus

    // 如果流式数据中有明确的状态，直接使用；否则才进行推断
    if (finalStatus === null || finalStatus === undefined) {
      finalAgentStatus = inferFinalAgentStatus(steps.map(s => s.step), finalStatus, finalCompleted)
      // console.log(`🔧 [updateAgentStatusInExecutionSteps] Agent ${agentId} 需要推断状态: ${finalAgentStatus}`)
    } else {
      // console.log(`🔧 [updateAgentStatusInExecutionSteps] Agent ${agentId} 使用流式数据状态: ${finalStatus}`)
    }


    // 更新该Agent的所有步骤
    steps.forEach(({ step, index }) => {
      const oldStatus = step.agentStatus
      const oldCompleted = step.agentCompleted

      step.agentStatus = finalAgentStatus
      step.agentCompleted = finalCompleted

      if (oldStatus !== finalAgentStatus || oldCompleted !== finalCompleted) {
        // console.log(`🔧 [updateAgentStatusInExecutionSteps] 更新步骤 ${index}: 状态 ${oldStatus} -> ${finalAgentStatus}, 完成 ${oldCompleted} -> ${finalCompleted}`)
      }
    })
  })
}

// 推断Agent的最终状态
const inferFinalAgentStatus = (steps, currentStatus, isCompleted) => {
  // 1. 如果明确标记为已完成
  if (isCompleted === true) {
    return 'completed'
  }

  // 2. 检查是否有terminate工具调用且已完成
  const hasTerminateStep = steps.some(step =>
    step.toolName === 'terminate' &&
    (step.status === 'FINISHED' || step.status === 'COMPLETED')
  )

  if (hasTerminateStep) {
    return 'completed'
  }

  // 3. 检查是否有失败的步骤
  const hasFailedStep = steps.some(step =>
    step.status === 'FAILED' || step.status === 'ERROR'
  )

  if (hasFailedStep) {
    return 'failed'
  }

  // 4. 如果当前状态明确是completed或failed，保持不变
  if (currentStatus === 'completed' || currentStatus === 'COMPLETED') {
    return 'completed'
  }

  if (currentStatus === 'failed' || currentStatus === 'FAILED') {
    return 'failed'
  }

  // 5. 检查最后一个步骤的状态
  if (steps.length > 0) {
    const lastStep = steps[steps.length - 1]

    if (lastStep.status === 'FINISHED' || lastStep.status === 'COMPLETED') {
      // 如果最后一个步骤完成了，但没有明确的完成标记，可能还在进行中
      return currentStatus || 'in_progress'
    }

    if (lastStep.status === 'RUNNING' || lastStep.status === 'IN_PROGRESS') {
      return 'in_progress'
    }
  }

  // 6. 默认返回当前状态或in_progress
  return currentStatus || 'in_progress'
}

// 🔧 新增：将步骤状态映射为Agent状态
const mapStepStatusToAgentStatus = (stepStatus) => {
  const statusMap = {
    'RUNNING': 'in_progress',
    'IN_PROGRESS': 'in_progress',
    'FINISHED': 'completed',
    'COMPLETED': 'completed',
    'FAILED': 'failed',
    'ERROR': 'failed',
    'PENDING': 'in_progress',
    'WAITING': 'in_progress'
  }
  return statusMap[stepStatus] || 'in_progress'
}

// 🔧 新增：为分组推断最终Agent状态
const inferFinalAgentStatusForGroup = (group) => {
  const { steps, agentCompleted, agentStatus } = group

  // 使用现有的推断逻辑
  return inferFinalAgentStatus(steps, agentStatus, agentCompleted)
}

// 移除了复杂的状态追踪器，因为现在直接在数据接收时更新状态

// 按Agent分组执行步骤（简化版，因为状态更新已在数据接收时完成）
const groupedExecutionSteps = (steps) => {
  const groups = new Map()

  steps.forEach((step) => {
    const agentId = step.agentExecutionId

    // 过滤掉最后一条数据：agentExecutionId为0，agentName是"SUMMARY_AGENT"，包含planSummary字段
    if (step.agentExecutionId === 0 && step.agentName === 'SUMMARY_AGENT') {
      return // 不添加到steps中
    }

    if (!groups.has(agentId)) {
      const newGroup = {
        agentExecutionId: agentId,
        agentName: step.agentName,
        agentDescription: step.agentDescription,
        agentStatus: step.agentStatus, // 直接使用已更新的状态
        agentCompleted: step.agentCompleted,
        steps: []
      }
      // console.log('newGroup++',newGroup)
      groups.set(agentId, newGroup)
    }

    // 🔧 修复：更新组的状态（使用最新步骤的状态）
    const group = groups.get(agentId)

    // 🔍 调试：记录状态更新过程
    const oldGroupStatus = group.agentStatus
    const oldGroupCompleted = group.agentCompleted

    // 总是更新agentStatus，后面的步骤覆盖前面的步骤
    if (step.agentStatus !== undefined) {
      group.agentStatus = step.agentStatus
      
    }

    // 总是更新agentCompleted状态
    if (step.agentCompleted !== undefined) {
      group.agentCompleted = step.agentCompleted
      
    }

    // 如果当前步骤没有agentStatus，但组还没有状态，尝试从步骤的status推断
    if (group.agentStatus === null && step.status) {
      const inferredStatus = mapStepStatusToAgentStatus(step.status)
      group.agentStatus = inferredStatus
      
    }

    group.steps.push(step)
  })

  // 🔧 修复：对每个组进行最终状态推断和验证
  groups.forEach((group, agentId) => {
    const finalStatus = inferFinalAgentStatusForGroup(group)
    if (finalStatus !== group.agentStatus) {
      
      group.agentStatus = finalStatus
    }
  })

  const result = Array.from(groups.values())

  return result
}

// 智能推断Agent状态（保留原函数作为备用）
const inferAgentStatus = (group) => {
  const { steps, agentCompleted, agentStatus } = group

  // 如果明确标记为已完成，返回completed
  if (agentCompleted === true) {
    return 'completed'
  }

  // 如果有步骤，分析步骤状态
  if (steps && steps.length > 0) {
    const lastStep = steps[steps.length - 1]

    // 检查是否有terminate工具调用且已完成
    const hasTerminateStep = steps.some(step =>
      step.toolName === 'terminate' &&
      (step.status === 'FINISHED' || step.status === 'COMPLETED')
    )

    if (hasTerminateStep) {
      return 'completed'
    }

    // 检查是否有失败的步骤
    const hasFailedStep = steps.some(step =>
      step.status === 'FAILED' || step.status === 'ERROR'
    )

    if (hasFailedStep) {
      return 'failed'
    }

    // 检查最后一个步骤的状态
    if (lastStep.status === 'FINISHED' || lastStep.status === 'COMPLETED') {
      // 如果最后一个步骤完成了，但没有terminate，可能还在进行中
      return agentStatus || 'in_progress'
    }

    if (lastStep.status === 'RUNNING' || lastStep.status === 'IN_PROGRESS') {
      return 'in_progress'
    }
  }

  // 默认返回原状态或in_progress
  return agentStatus || 'in_progress'
}

// 工具状态相关方法
const getToolStatusClass = (status: string) => {
  const statusMap = {
    'RUNNING': 'status-running',
    'FINISHED': 'status-completed',
    'FAILED': 'status-failed',
    'PENDING': 'status-pending'
  }
  return statusMap[status] || 'status-unknown'
}

const getToolStatusText = (status: string) => {
  const statusMap = {
    'RUNNING': '执行中',
    'FINISHED': '已完成',
    'FAILED': '执行失败',
    'PENDING': '等待中'
  }
  return statusMap[status] || status
}

// 监听路由参数变化
watch(() => route.params.workflowId, (newWorkflowId) => {
  if (newWorkflowId) {
    currentChatId.value = 'chat-' + Date.now() + '-' + Math.random().toString(36).substring(2)
    workflowName.value = route.query.workflowName as string || ''
    currentChatTitle.value = workflowName.value || '智能对话'

    // 更新新对话设置中的计划模板ID
    if (route.query.planTemplateId) {
      newChatSettings.value.planTemplateId = route.query.planTemplateId as string
    }
  }
}, { immediate: true })

// 监听conversationRounds变化，自动渲染图表
watch(
  () => conversationRounds.value,
  (newRounds) => {
    nextTick(() => {
      newRounds.forEach((round) => {
        console.log('监听conversationRounds变化，自动渲染图表',round)
        if (round.chartData && round.chartData.length > 0) {
          round.chartData.forEach((chartItem) => {
            const containerId = `summary-chart-${chartItem.stepId}`

            // 初始化默认激活的tab（柱状图优先）
            if (!activeChartTabs.value.has(chartItem.stepId)) {
              activeChartTabs.value.set(chartItem.stepId, 'bar')
            }

            // 获取当前激活的tab类型
            const activeTab = getActiveChartTab(chartItem.stepId)

            // 如果当前激活的是图表类型，则渲染图表
            if (activeTab !== 'table') {
              // 检查是否已经渲染过此图表
              const renderKey = `${containerId}-${activeTab}`
              if (renderedCharts.value.has(renderKey)) {
                console.log('图表已渲染，跳过:', renderKey)
                return
              }

              console.log('渲染图表，容器ID:', containerId, '图表类型:', activeTab, '图表数据:', chartItem)

              // 创建修改后的图表项，使用当前激活的图表类型
              const modifiedChartItem = { ...chartItem, chartType: activeTab }
              renderSummaryChart(modifiedChartItem, containerId)

              // 标记为已渲染
              renderedCharts.value.add(renderKey)
            }
          })
        }
      })
    })
  },
  { deep: true }
)

onMounted(async () => {

  // 从路由参数获取助手名称和ID
  const routeId = route.query.id as string || ''
  const routeName = route.query.name as string || ''

  // 初始化新对话设置
  // newChatSettings.value.userId = currentUserId.value

  // 并行加载模型列表和助手列表
  await Promise.all([
    loadModelList(),
    loadAssistantsList()
  ])

  // 处理路由参数逻辑
  if (!routeId && !routeName) {
    // 当页面路由上获取不到id和name时，默认获取助手列表的第一个信息
    await handleDefaultAssistant()
  } else {
    // 有路由参数时，设置助手名称和planTemplateId
    assistantName.value = decodeURIComponent(routeName) || ''
    if (routeId) {
      newChatSettings.value.planTemplateId = routeId
      // 加载该助手的历史会话
      await refreshHistory(historySearchKeyword.value.trim())
    }
  }
})

// 组件销毁时清理图表资源
onBeforeUnmount(() => {
  // 清理所有图表实例
  chartInstances.value.forEach((chart, containerId) => {
    if ((chart as any)._resizeHandler) {
      window.removeEventListener('resize', (chart as any)._resizeHandler)
    }
    chart.dispose()
  })
  chartInstances.value.clear()
  // 清理所有渲染标记
  renderedCharts.value.clear()
  
  // 清理拖拽相关的事件监听器
  if (currentThrottledResize) {
    document.removeEventListener('mousemove', currentThrottledResize as any)
    document.removeEventListener('touchmove', currentThrottledResize as any)
    currentThrottledResize = null
  }
  document.removeEventListener('mouseup', stopResize)
  document.removeEventListener('touchend', stopResize)
})

// 处理默认助手逻辑
const handleDefaultAssistant = async () => {
  try {
    if (assistantsList.value && assistantsList.value.length > 0) {
      const firstAssistant = assistantsList.value[0]

      // 设置助手名称
      assistantName.value = firstAssistant.title || firstAssistant.name || 'ChatBI'

      // 设置planTemplateId - 根据实际API响应结构，可能是id字段就是planTemplateId
      const planTemplateId = firstAssistant.planTemplateId || firstAssistant.id
      if (planTemplateId) {
        newChatSettings.value.planTemplateId = planTemplateId

        // 获取该助手的历史会话
        await loadAssistantHistory(planTemplateId)
      } else {
        console.warn('第一个助手没有planTemplateId或id字段')
        // 如果没有planTemplateId，设置空的历史
        chatHistory.value = []
      }
    } else {
      console.warn('助手列表为空，使用默认配置')
      assistantName.value = 'ChatBI'
      // 设置空的历史
      chatHistory.value = []
    }
  } catch (error) {
    console.error('处理默认助手失败:', error)
    assistantName.value = 'ChatBI'
    chatHistory.value = []
  }
}

// 监听对话轮次变化，渲染图表
watch(conversationRounds, () => {
  nextTick(() => {
    // 渲染所有需要图表的对话轮次
    conversationRounds.value.forEach(round => {
      const chartData = getSqlQueryChartData(round)
      if (chartData) {
        // 设置默认页签
        if (!activeTabsMap.value.has(round.id)) {
          // 如果有图表配置，默认显示图表；否则显示表格
          const defaultTab = chartData.chartConfig ? 'chart' : 'table'
          activeTabsMap.value.set(round.id, defaultTab)
        }

        // 只有在图表页签激活且有图表配置时才渲染图表
        if (getActiveTab(round.id) === 'chart' && chartData.chartConfig) {
          const chartId = `chart-${round.id}`
          // 延迟渲染，确保DOM已更新
          setTimeout(() => {
            renderChart(chartData.chartConfig, chartId)
          }, 100)
        }
      }
    })
  })
}, { deep: true })
</script>

<style lang="less" scoped>
/* 光影流动动画效果 */
.loading-text-shimmer {
  position: relative;
  display: inline-block;
  color: #6b7280;
  font-size: 14px;
  font-weight: 500;
  padding: 8px 16px;
  background: linear-gradient(90deg, #f3f4f6 25%, #e5e7eb 50%, #f3f4f6 75%);
  background-size: 200% 100%;
  border-radius: 6px;
  overflow: hidden;
  
  /* 创建光影效果 */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.6),
      transparent
    );
    animation: shimmer 2s infinite;
  }
  
  /* 文字颜色渐变动画 */
  background: linear-gradient(
    90deg,
    #9ca3af 0%,
    var(--el-color-primary) 25%,
    #8b5cf6 50%,
    var(--el-color-primary) 75%,
    #9ca3af 100%
  );
  background-size: 200% 100%;
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: textShimmer 3s ease-in-out infinite;
}

/* 光影流动关键帧动画 */
@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* 文字颜色渐变关键帧动画 */
@keyframes textShimmer {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

.chat-page {
  // width: 100%;
  height: calc(100vh - 60px);
  color: #1f2937;
  border: 1px solid #e6e8ee;
  background-color: #fff;
  border-radius: 8px;
  margin-right: 8px;
  // margin: 0 8px 8px 0;
}

.app-container {
  display: flex;
  height: calc(100vh - 62px);
  // width: calc(100vw - 210px);
}

/* 侧边栏样式 */
.sidebar {
  width: 260px;
  // background: white;
  border-right: 1px solid #e5e7eb;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
  position: relative;
  border-top-left-radius: 8px;
  &.collapsed {
    width: 2px;
  }
}

.sidebar-header {
  padding: 6px 20px;
  border-bottom: 1px solid #e5e7eb;
}

.app-title {
  font-size: 14px;
  // font-weight: 700;
  // color: var(--el-color-primary);
  // margin-bottom: 15px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 10px;
}

.title-left {
  display: flex;
  align-items: center;
  gap: 10px;
  min-width: 0; /* 允许flex子元素收缩 */
}

.title-left span {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  min-width: 0; /* 允许文本收缩 */
}

.back-to-dashboard-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: #f3f4f6;
  // border: 1px solid #d1d5db;
  border-radius: 6px;
  color: #374151;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: fit-content;
  &:hover {
    background: #e5e7eb;
    border-color: #9ca3af;
  }
}

.user-info {
  margin: 15px 0;
  padding: 12px;
  background: #f9fafb;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.user-id-display {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #1f2937;
}

.user-id-edit {
  margin-top: 10px;
}

.edit-actions {
  display: flex;
  gap: 8px;
  margin-top: 8px;
}

/* Tab切换样式 */
.sidebar-tabs {
  // padding: 0 20px 15px;
  border-bottom: 1px solid #e5e7eb;
}

.tab-buttons {
  display: flex;
  gap: 4px;
  background: #f9fafb;
  border-radius: 8px;
  padding: 4px;
}

.tab-button {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  padding: 8px 12px;
  background: transparent;
  border: none;
  border-radius: 6px;
  color: #6b7280;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    color: #374151;
    background: #f3f4f6;
  }

  &.active {
    background: white;
    color: var(--el-color-primary);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }
}

/* 助手列表样式 */
.assistants-list {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.assistants-content {
  flex: 1;
  overflow-y: auto;
  padding: 0 10px;
  margin-top: 10px;
}

.assistant-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 10px;
  margin-bottom: 8px;
  background: white;
  // border: 1px solid #e5e7eb;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
  &:hover {
    // box-shadow: 0 2px 8px rgba(99, 102, 241, 0.1);
    background: #f9fafb;
    border-color: #e5e7eb;
  }
  &.active {
    background: var(--el-color-primary);
    color: white;
    border-color: var(--el-color-primary);
    font-weight: 600;
  }
  &:last-child {
    margin-bottom: 0;
  }
}

.assistant-avatar {
  width: 36px;
  height: 36px;
  background: var(--el-color-primary);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
  flex-shrink: 0;
}

.assistant-info {
  flex: 1;
  min-width: 0;
}

.assistant-name {
  font-size: 14px;
  font-weight: 400;
  // color: #1f2937;
  margin-bottom: 4px;
  line-height: 1.3;
  word-break: break-all;
}

.assistant-description {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 4px;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.new-chat-btn {
  // width: 100%;
  background: rgba(205, 208, 220, 0.8);
  color: #000;
  font-size: 14px;
  border: none;
  padding: 4px 6px;
  border-radius: 8px;
  // font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 3px;
  transition: all 0.2s ease;

  &:hover {
    // background: #8b5cf6;
    transform: translateY(-1px);
  }
}

.new-chat-settings {
  margin-top: 15px;
  padding: 15px;
  background: #f9fafb;
  border-radius: 8px;
}

.form-group {
  margin-bottom: 15px;
}

.form-label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #1f2937;
  font-size: 14px;
}

.form-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s ease;

  &:focus {
    outline: none;
    // border-color: var(--el-color-primary);
    // box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
  }
}

.form-hint {
  color: #6b7280;
  font-size: 0.75rem;
  margin-top: 4px;
  display: block;
}

.form-actions {
  display: flex;
  gap: 8px;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 6px;

  &.btn-primary {
    background: var(--el-color-primary);
    color: white;

    &:hover {
      background: #8b5cf6;
    }
  }

  &.btn-secondary {
    background: #6b7280;
    color: white;

    &:hover {
      background: #1f2937;
    }
  }
}

.btn-icon {
  background: transparent;
  border: none;
  color: #6b7280;
  padding: 4px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;

  &:hover {
    background: #f9fafb;
    color: var(--el-color-primary);
  }
}

/* 历史会话样式 */
.chat-history {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.new-chat-section {
  padding: 15px 20px;
  border-bottom: 1px solid #e5e7eb;
}

.new-chat-btn-full {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 8px 10px;
  background: var(--el-color-primary);
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: var(--el-color-primary-light-3);
  }

  &:active {
    transform: translateY(1px);
  }
}

.history-header {
  padding: 15px 20px 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e5e7eb;

  h5 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    color: #1f2937;
  }
}

.history-search {
  padding: 10px 20px;
}

.search-input-wrapper {
  position: relative;
}

.search-input {
  width: 100%;
  // padding: 8px 12px 8px 32px;
  // border: 1px solid #e5e7eb;
  border-radius: 6px;
  font-size: 0.75rem;
  // background: #f9fafb;
  transition: border-color 0.2s ease;

  &:focus {
    outline: none;
    border-color: var(--el-color-primary);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
  }
}

.search-icon {
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: #6b7280;
  font-size: 0.75rem;
}

.history-content {
  flex: 1;
  overflow-y: auto;
  padding: 0 10px;
}

.loading-placeholder {
  text-align: center;
  padding: 20px;
  color: #6b7280;
  font-size: 14px;
}

.empty-assistants {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 40px 20px;
  color: #6b7280;
  font-size: 0.875rem;
}

.empty-history {
  text-align: center;
  padding: 40px 20px;
  color: #6b7280;
  font-size: 0.875rem;
}

.history-group-title {
  font-size: 0.75rem;
  font-weight: 600;
  color: #6b7280;
  padding: 8px 10px 4px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.history-item {
  padding: 10px;
  margin: 2px 0;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;

  &:hover {
    background: #f9fafb;
    border-color: #e5e7eb;
  }

  &.active {
    background: var(--el-color-primary);
    color: white;
    border-color: var(--el-color-primary);
    font-weight: 600;
  }
}

.history-item-title {
  font-size: 14px;
  font-weight: 400;
  margin-bottom: 4px;
  line-height: 1.2;
  display: -webkit-box;
  line-clamp: 2;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.history-item-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.75rem;
  opacity: 1;
}

.history-item-time {
  color: #6b7280;
}

.history-item.active .history-item-time {
  color: rgba(255, 255, 255, 0.85);
}

.history-item-actions {
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.history-item:hover .history-item-actions {
  opacity: 1;
}

.history-item-action {
  background: transparent;
  border: none;
  color: #6b7280;
  padding: 2px;
  border-radius: 3px;
  cursor: pointer;
  font-size: 1.05rem;

  &:hover {
    background: rgba(0, 0, 0, 0.1);
  }
}

.history-item.active .history-item-action {
  color: rgba(255, 255, 255, 1);

  &:hover {
    background: rgba(255, 255, 255, 0.2);
  }
}

.sidebar-toggle {
  position: absolute;
  right: -12px;
  top: 50%;
  transform: translateY(-50%);
  width: 24px;
  height: 24px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 0.75rem;
  color: #6b7280;
  transition: all 0.2s ease;
  z-index: 10;

  &:hover {
    background: #f9fafb;
    color: var(--el-color-primary);
  }
}

/* 主内容区域 */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  // background: white;
  transition: margin-right 0.3s ease;
  width: 100%;
  &.with-right-panel {
    margin-right: 400px;
  }
}

.chat-header {
  padding: 20px 30px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
}

.chat-title-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.back-btn {
  background: transparent;
  border: 1px solid #e5e7eb;
  color: #6b7280;
  padding: 8px;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;

  &:hover {
    background: #f9fafb;
    border-color: var(--el-color-primary);
    color: var(--el-color-primary);
  }
}

.chat-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.chat-subtitle {
  font-size: 0.875rem;
  color: #6b7280;
  margin-left: 8px;
}

.chat-actions {
  display: flex;
  gap: 10px;
}

.action-btn {
  background: transparent;
  border: 1px solid #e5e7eb;
  color: #6b7280;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.875rem;
  transition: all 0.2s ease;

  &:hover {
    background: #f9fafb;
    border-color: var(--el-color-primary);
    color: var(--el-color-primary);
  }
}

.chat-container {
  flex: 1;
  overflow-y: auto;
  padding: 20px 30px;
  // background: #f9fafb;
}

.message {
  margin-bottom: 20px;
  display: flex;
  // gap: 12px;
  animation: messageSlideIn 0.3s ease;
}

/* 用户消息靠右显示 */
.user-message {
  // justify-content: flex-end;
  // flex-direction: row-reverse;
}

@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.message-avatar {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  flex-shrink: 0;
}

.user-message .message-avatar {
  background: var(--el-color-primary);
  color: white;
  margin-top: 2px;
}

.system-message .message-avatar {
  background: #f59e0b;
  color: white;
}

.message-content {
  flex: 1;
  background: white;
  padding: 12px 16px;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  position: relative;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.user-message .message-content {
  background: transparent;
  color: #1f2937;
  border: none;
  box-shadow: none;
  flex: none;
  max-width: 70%;
  padding: 8px 12px;
}

.message-time {
  font-size: 0.75rem;
  color: #6b7280;
  margin-top: 8px;
}

.user-message .message-time {
  color: #6b7280;
}

.conversation-round {
  margin-bottom: 24px;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 16px;

  &:last-child {
    border-bottom: none;
    margin-bottom: 0;
  }
}

.ai-response-container {
  margin-top: 12px;
}

/* 配置区域样式 */
.chat-config {
  padding: 2px 0px;
}

.config-row {
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-wrap: wrap;
}

.config-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  min-width: 200px;
}

.config-item.datasource-config {
  flex: 2;
  min-width: 300px;
}

.config-item.model-config {
  flex: 1;
  min-width: 200px;
}

.config-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  white-space: nowrap;
}

.config-select {
  padding: 0.75rem 1rem;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  background: white;
  font-size: 0.875rem;
  color: #374151;
  transition: all 0.2s ease;
  width: fit-content;
  min-width: 200px;
  cursor: pointer;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

  /* 设置下拉箭头颜色 */
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%239ca3af' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.75rem center;
  background-repeat: no-repeat;
  background-size: 1.25em 1.25em;
  padding-right: 3rem;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;

  &:hover {
    border-color: #d1d5db;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
  }
}

.config-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.config-select:disabled {
  background: #f3f4f6;
  color: #9ca3af;
  cursor: not-allowed;
}

/* 模型选择框特殊样式 */
.model-select {
  font-family: var(--el-font-family);

  option {
    padding: 0.5rem;
    font-size: 0.875rem;
    line-height: 1.4;

    /* 尝试为option添加样式，但浏览器支持有限 */
    &[data-config-name]:not([data-config-name=""]) {
      font-weight: 600;
    }
  }
}


/* 输入区域 */
.chat-input {
  padding: 10px 20px;
  border-top: 1px solid #e5e7eb;
  // background: white;
}

.input-container {
  display: flex;
  gap: 12px;
  align-items: flex-end;
}

.message-input-wrapper {
  flex: 1;
  position: relative;
}

.message-input {
  width: 100%;
  min-height: 44px;
  max-height: 120px;
  padding: 12px 16px;
  border: 1px solid transparent;
  border-radius: 12px;
  resize: none;
  font-family: inherit;
  font-size: 0.875rem;
  line-height: 1.5;
  transition: border-color 0.2s ease;
  padding-left: 11px;
  &:focus {
    outline: none;
    // border-color: var(--el-color-primary);
    // box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
  }
  &::placeholder {
    color: #9ca3af;
  }
}

.send-btn {
  background: var(--el-color-primary);
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 500;
  transition: all 0.2s ease;

  &:hover:not(:disabled) {
    background: var(--el-color-primary-light-3);
    transform: translateY(-1px);
  }

  &:disabled {
    background: var(--el-color-info);
    cursor: not-allowed;
    transform: none;
  }

  // 停止状态的特殊样式
  &.stop-mode {
    background: var(--el-color-danger);

    &:hover:not(:disabled) {
      background: var(--el-color-danger-light-3);
    }
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar {
    width: 280px;

    &.collapsed {
      width: 0;
      border-right: none;
    }
  }

  .chat-header {
    padding: 16px 20px;
  }

  .chat-container {
    padding: 16px 20px;
  }

  .chat-config {
    padding: 12px 20px;
  }

  .config-row {
    flex-direction: column;
    gap: 0.75rem;
    align-items: stretch;
  }

  .config-item {
    min-width: auto;
    flex-direction: column;
    align-items: stretch;
    gap: 0.25rem;
    width: fit-content;
  }

  .config-label {
    font-size: 0.8rem;
  }

  .config-select {
    min-width: auto;
  }

  .chat-input {
    padding: 10px 20px;
  }

  .chat-title {
    font-size: 1.25rem;
  }

  .input-container {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }
}

/* 层次化组件样式 */
.agents-container {
  margin: 12px 0;
}

.think-container {
  margin: 12px 0;
}

.tools-container {
  margin: 12px 0;
}

/* 新的执行步骤容器样式 */
.execution-steps-container {
  margin: 16px 0;
  // border: 1px solid #8b5cf6;
  border-radius: 12px;
  // background: linear-gradient(135deg, rgba(139, 92, 246, 0.05), rgba(139, 92, 246, 0.02));
  // overflow: hidden;
  padding-left: 40px; /* 为时间线节点留出空间 */
  position: relative;
}

.steps-header {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
  color: white;
  padding: 12px 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  font-size: 0.875rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.steps-count {
  background: rgba(255, 255, 255, 0.2);
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 0.75rem;
  margin-left: auto;
}

.summary-container {
  margin: 16px 0;
  border: 1px solid var(--el-color-primary);
  border-radius: 8px;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.05), rgba(99, 102, 241, 0.02));
  overflow: hidden;
}

.summary-header {
  background: var(--el-color-primary);
  color: white;
  padding: 8px 12px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  font-size: 14px;
}

.summary-content {
  padding: 12px;
  font-size: 14px;
  line-height: 1.5;
  white-space: pre-wrap;
}

/* 右侧工具详情面板 */
.right-panel {
  position: fixed;
  top: 0;
  right: 0;
  width: 500px;
  height: 100vh;
  background: white;
  border-left: 1px solid #e5e7eb;
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  animation: panelSlideIn 0.3s ease;
  resize: horizontal;
  overflow: auto;
}

/* 拖拽调整大小的把手 */
.resize-handle {
  position: absolute;
  left: 0;
  top: 0;
  width: 5px;
  height: 100%;
  cursor: ew-resize;
  background-color: transparent;
  z-index: 1001;
}

.resize-handle:hover, .resize-handle:active {
  background-color: var(--el-color-primary-light);
}

@keyframes panelSlideIn {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}

.panel-header {
  padding: 16px 20px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #f8fafc;
}

.panel-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #1f2937;
  font-size: 0.875rem;
}

.close-btn {
  background: transparent;
  border: none;
  color: #6b7280;
  padding: 4px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: #e5e7eb;
    color: #374151;
  }
}

.panel-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.loading-section {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px 20px;
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  color: #6b7280;
  font-size: 0.875rem;
}

.spinning {
  animation: spin 1s linear infinite;
  font-size: 1.5rem;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.error-section {
  margin-bottom: 16px;
}

.error-message {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
  color: #dc2626;
  font-size: 0.875rem;
}

.tool-detail-section {
  margin-bottom: 24px;

  h4 {
    margin: 0 0 12px 0;
    font-size: 0.875rem;
    font-weight: 600;
    color: #374151;
    border-bottom: 1px solid #e5e7eb;
    padding-bottom: 8px;
  }
}

.json-viewer {
  background: #f8f9fa;
    border: 1px solid #e9ecef;
    color: #495057;
  border-radius: 8px;
  overflow: hidden;

  pre {
    padding: 16px;
    margin: 0;
    font-family: 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.5;
    overflow-x: auto;
    white-space: pre-wrap;
    word-break: break-all;
  }
}
.step-container-summary{
  padding: 16px 0px;
  margin-top: -20px;
}

/* Summary区域图表样式 */
.summary-charts-container {
  margin-bottom: 20px;
}

.summary-chart-item {
  margin-bottom: 24px;
  // border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
  background: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.summary-table-container,
.summary-chart-container {
  padding: 16px;
}

.summary-multi-view-container .summary-table-container,
.summary-multi-view-container .summary-chart-container {
  padding: 16px;
}

.chart-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  justify-items: center;
  gap: 8px;
}

.chart-title::before {
  // content: "📊";
  // font-size: 18px;
}

/* 饼图特殊图标 */
.summary-chart-item[data-chart-type="pie"] .chart-title::before {
  // content: "🥧";
}

.summary-chart-canvas {
  border-radius: 4px;
  background: #fff;
}

/* 多视图容器样式 */
.summary-multi-view-container {
  margin-bottom: 24px;
  border-radius: 8px;
  overflow: hidden;
  background: #fff;
  // box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

  .chart-title {
    // font-size: 18px;
    // font-weight: 600;
    color: #1f2937;
    margin: 0;
    // padding: 16px 16px 0 16px;
    border-bottom: none;
    justify-content: center;
  }
}

/* Tab切换按钮样式 */
.chart-view-tabs {
  display: flex;
  gap: 8px;
  background: #f8fafc;
  // border-bottom: 1px solid #e2e8f0;
  padding: 8px 16px;
}

.tab-icon-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  background: transparent;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: #f1f5f9;
    border-color: #cbd5e1;
    color: #475569;
  }

  &.active {
    background: #3b82f6;
    border-color: #3b82f6;
    color: white;
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
  }

  .el-icon {
    font-size: 16px;
  }
}


/* 新增的结果渲染样式 */
.result-table-container {
  border: 1px solid #e9ecef;
  border-radius: 6px;
  overflow: hidden;
  background: #fff;
}

.result-chart-container {
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 16px;
  background: #fff;
}

.result-sql-container {
  border: 1px solid #e9ecef;
  border-radius: 6px;
  background: #fff;
  overflow: hidden;
}

.result-markdown-container {
  border: 1px solid #e9ecef;
  border-radius: 6px;
  background: #fff;
  overflow: hidden;
}

.markdown-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  font-weight: 500;
  color: #495057;
  font-size: 14px;
}

.markdown-content {
  padding: 16px;
  line-height: 1.6;
  color: #333;
  font-size: 14px;
  max-height: 570px;
  overflow-y: auto;

 

  /* 段落样式 */
  p {
    margin: 8px 0;
    line-height: 1.6;
  }

  /* 列表样式 */
  ul, ol {
    margin: 8px 0;
    padding-left: 20px;
  }

  li {
    margin: 4px 0;
  }

  /* 代码样式 */
  code {
    background: #f1f3f4;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
    font-size: 0.85em;
    color: #d73a49;
  }

  pre {
    background: #f6f8fa;
    border: 1px solid #e1e4e8;
    border-radius: 6px;
    padding: 12px;
    margin: 12px 0;
    overflow-x: auto;
    font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
    font-size: 0.8rem;
    line-height: 1.4;

    code {
      background: none;
      padding: 0;
      color: inherit;
    }
  }

  /* 引用样式 */
  blockquote {
    border-left: 4px solid #dfe2e5;
    padding: 0 16px;
    margin: 12px 0;
    color: #6a737d;
    background: #f6f8fa;
  }

  /* 链接样式 */
  a {
    color: #0366d6;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }

  /* 表格样式 */
  table {
    border-collapse: collapse;
    width: 100%;
    margin: 12px 0;
    font-size: 0.8rem;
  }

  th, td {
    border: 1px solid #dfe2e5;
    padding: 6px 12px;
    text-align: left;
  }

  th {
    background: #f6f8fa;
    font-weight: 600;
  }

  /* 水平分割线 */
  hr {
    border: none;
    border-top: 1px solid #e1e4e8;
    margin: 16px 0;
  }

  /* 删除线 */
  del {
    color: #6a737d;
  }

  /* 强调样式 */
  strong {
    font-weight: 600;
  }

  em {
    font-style: italic;
  }
}

.sql-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  font-weight: 500;
  color: #495057;
}

.sql-content {
  padding: 16px;
}

.sql-code {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 12px;
  font-family: 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.5;
  color: #495057;
  white-space: pre-wrap;
  word-wrap: break-word;
  margin: 0;
  max-height: 300px;
  overflow-y: auto;
}

/* 测试按钮样式 */
.test-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.test-btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: background-color 0.2s;
}

.test-btn:hover {
  background: #0056b3;
}

.info-grid {
  display: grid;
  gap: 12px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f9fafb;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
}

.info-item .label {
  font-weight: 500;
  color: #6b7280;
  font-size: 0.75rem;
}

.info-item .value {
  font-weight: 500;
  color: #1f2937;
  font-size: 0.75rem;

  &.status-running {
    color: #f59e0b;
  }

  &.status-completed {
    color: #10b981;
  }

  &.status-failed {
    color: #ef4444;
  }

  &.status-pending {
    color: #6b7280;
  }
}

/* 图表展示容器样式 */
.chart-display-container {
  margin: 16px 0;
  padding: 16px;
  background: rgba(16, 185, 129, 0.05);
  border: 1px solid rgba(16, 185, 129, 0.2);
  border-radius: 8px;
  margin-left: 40px;
  .chart-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 16px;
    font-weight: 600;
    color: #10b981;
    font-size: 14px;
  }

  .chart-content {
    .chart-tabs {
      display: flex;
      gap: 4px;
      margin-bottom: 16px;
      border-bottom: 1px solid #e5e7eb;

      .tab-button {
        display: flex;
        align-items: center;
        gap: 6px;
        padding: 8px 16px;
        background: transparent;
        border: none;
        border-bottom: 2px solid transparent;
        color: #6b7280;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
          color: #10b981;
          background: rgba(16, 185, 129, 0.05);
        }

        &.active {
          color: #10b981;
          border-bottom-color: #10b981;
          background: rgba(16, 185, 129, 0.1);
        }
      }
    }

    .chart-panel, .table-panel {
      .chart-canvas {
        border-radius: 6px;
        background: white;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      }
    }

    .table-panel {
      .table-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;
        padding: 8px 12px;
        background: rgba(16, 185, 129, 0.1);
        border-radius: 6px;
        font-size: 0.75rem;
        color: #065f46;
        font-weight: 500;
      }

      .table-container {
        max-height: 400px;
        overflow: auto;
        border-radius: 6px;
        border: 1px solid #e5e7eb;
        background: white;

        .data-table {
          width: 100%;
          border-collapse: collapse;
          font-size: 14px;

          thead {
            background: #f9fafb;
            position: sticky;
            top: 0;
            z-index: 1;

            th {
              padding: 12px 8px;
              text-align: left;
              font-weight: 600;
              color: #374151;
              border-bottom: 1px solid #e5e7eb;
              white-space: nowrap;
            }
          }

          tbody {
            tr {
              &:nth-child(even) {
                background: #f9fafb;
              }

              &:hover {
                background: rgba(16, 185, 129, 0.05);
              }

              td {
                padding: 8px;
                border-bottom: 1px solid #f3f4f6;
                color: #374151;
                max-width: 200px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }
            }
          }
        }
      }
    }
  }
}

/* 响应式适配 */
@media (max-width: 1200px) {
  .right-panel {
    width: 350px;
  }

  .main-content.with-right-panel {
    margin-right: 350px;
  }
}

@media (max-width: 768px) {
  .right-panel {
    width: 100%;
  }

  .main-content.with-right-panel {
    margin-right: 0;
    display: none;
  }
}

</style>
<style>
 /* 标题样式 */
  h1, h2, h3, h4, h5, h6 {
    margin: 16px 0 8px 0;
    font-weight: 600 !important;
    line-height: 1.4;
    color: #1a1a1a;
  }

  h1 { font-size: 1.5rem; }
  h2 { font-size: 1.3rem; }
  h3 { font-size: 1.1rem; }
  h4 { font-size: 1rem; }
  h5 { font-size: 0.9rem; }
  h6 { font-size: 0.85rem; }
  hr{
    border-top-width: 0;
  }
  .markdown-body table {
  width: 100;
  border-collapse:collapse;
  margin: 10px 0;
}
.markdown-body th,
.markdown-body td {
  border: 1px solid #ddd;/*添加边框 */
  padding:8px;
  text-align: left;
}
.markdown-body th {
  background-color: #f9fafb;
  /*表头背景色 */font-weight: bold;
}
.markdown-body blockquote {
    color: #666;
    padding: 1px 23px;
    margin: 22px 0;
    border-left: 4px solid #cbcbcb;
    background-color: #f8f8f8;
}
</style>