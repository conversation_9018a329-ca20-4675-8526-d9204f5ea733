import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { ElMessage } from 'element-plus'
import ApiKeyManagement from '/@/views/team/pages/apikey/index.vue'

// Mock Element Plus message
vi.mock('element-plus', async () => {
  const actual = await vi.importActual('element-plus')
  return {
    ...actual,
    ElMessage: {
      success: vi.fn(),
      error: vi.fn(),
      warning: vi.fn()
    }
  }
})

// Mock clipboard API
Object.assign(navigator, {
  clipboard: {
    writeText: vi.fn().mockResolvedValue(undefined)
  }
})

describe('ApiKeyManagement', () => {
  let wrapper: any

  beforeEach(() => {
    wrapper = mount(ApiKeyManagement, {
      global: {
        stubs: {
          'el-button': true,
          'el-table': true,
          'el-table-column': true,
          'el-pagination': true,
          'el-dialog': true,
          'el-form': true,
          'el-form-item': true,
          'el-select': true,
          'el-option': true,
          'el-input': true,
          'el-popconfirm': true
        }
      }
    })
  })

  it('renders correctly', () => {
    expect(wrapper.find('.apikey-management-container').exists()).toBe(true)
    expect(wrapper.find('.page-title').text()).toBe('密钥管理')
  })

  it('displays create button with correct text', () => {
    const createButton = wrapper.find('[data-testid="create-button"]')
    expect(createButton.exists()).toBe(true)
  })

  it('formats API key correctly', async () => {
    const component = wrapper.vm
    
    // Test short API key
    expect(component.formatApiKey('sk-123')).toBe('sk-123')
    
    // Test long API key
    expect(component.formatApiKey('sk-abcdefghijklmnop')).toBe('sk-a****mnop')
    
    // Test empty API key
    expect(component.formatApiKey('')).toBe('')
  })

  it('formats time correctly', async () => {
    const component = wrapper.vm
    const testTime = '2025-06-16 17:13:39'
    const formatted = component.formatTime(testTime)
    
    expect(formatted).toContain('2025')
    expect(formatted).toContain('6')
    expect(formatted).toContain('16')
  })

  it('copies API key to clipboard', async () => {
    const component = wrapper.vm
    const testApiKey = 'sk-test123456789'
    
    await component.copyApiKey(testApiKey)
    
    expect(navigator.clipboard.writeText).toHaveBeenCalledWith(testApiKey)
    expect(ElMessage.success).toHaveBeenCalledWith('API Key已复制到剪贴板')
  })

  it('handles add operation', async () => {
    const component = wrapper.vm
    
    component.handleAdd()
    
    expect(component.isEdit).toBe(false)
    expect(component.dialogVisible).toBe(true)
    expect(component.formData.teamId).toBe('')
    expect(component.formData.description).toBe('')
  })

  it('handles edit operation', async () => {
    const component = wrapper.vm
    const mockRow = {
      id: 1,
      teamId: '124168624644841',
      description: 'test description'
    }
    
    component.handleEdit(mockRow)
    
    expect(component.isEdit).toBe(true)
    expect(component.dialogVisible).toBe(true)
    expect(component.formData.id).toBe(1)
    expect(component.formData.teamId).toBe('124168624644841')
    expect(component.formData.description).toBe('test description')
  })

  it('handles delete operation', async () => {
    const component = wrapper.vm
    
    // Set up initial data
    component.apiKeyList = [
      { id: 1, apiKey: 'sk-test1', teamId: '123', description: 'test1' },
      { id: 2, apiKey: 'sk-test2', teamId: '456', description: 'test2' }
    ]
    component.pagination.total = 2
    
    const mockRow = { id: 1, apiKey: 'sk-test1', teamId: '123', description: 'test1' }
    
    await component.handleDelete(mockRow)
    
    expect(component.apiKeyList).toHaveLength(1)
    expect(component.apiKeyList[0].id).toBe(2)
    expect(component.pagination.total).toBe(1)
    expect(ElMessage.success).toHaveBeenCalledWith('删除成功')
  })

  it('handles pagination changes', () => {
    const component = wrapper.vm

    // Mock the loadApiKeyList method to prevent actual API calls
    component.loadApiKeyList = vi.fn()

    // Test page size change
    component.handleSizeChange(20)
    expect(component.pagination.pageSize).toBe(20)
    expect(component.pagination.currentPage).toBe(1)

    // Test page change
    component.handleCurrentChange(2)
    expect(component.pagination.currentPage).toBe(2)
  })

  it('validates form rules', () => {
    const component = wrapper.vm
    
    expect(component.formRules.teamId).toBeDefined()
    expect(component.formRules.teamId[0].required).toBe(true)
    expect(component.formRules.teamId[0].message).toBe('请选择归属团队')
    
    expect(component.formRules.description).toBeDefined()
    expect(component.formRules.description[0].max).toBe(200)
  })

  it('computes dialog title correctly', () => {
    const component = wrapper.vm
    
    component.isEdit = false
    expect(component.dialogTitle).toBe('创建API-KEY')
    
    component.isEdit = true
    expect(component.dialogTitle).toBe('编辑API-KEY')
  })

  it('computes empty text correctly', () => {
    const component = wrapper.vm
    
    component.tableLoading = true
    expect(component.emptyText).toBe('加载中...')
    
    component.tableLoading = false
    expect(component.emptyText).toBe('暂无数据')
  })
})
