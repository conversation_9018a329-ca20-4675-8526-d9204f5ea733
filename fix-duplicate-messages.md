# 修复多轮对话消息重复显示问题

## 问题描述
在多轮对话中，发送消息时会出现多次显示，正常情况下应该只显示一次最后一次的消息。

## 问题原因分析
通过分析代码发现，问题出现在 `handleDirectExecutionResponse` 函数中：

1. **多处状态重置**：在处理 `SUMMARY_AGENT` 时，有多个地方会设置 `sending.value = false`
2. **重复历史刷新**：`refreshHistory()` 被多次调用
3. **流程冲突**：SUMMARY_AGENT 处理完成时和 onComplete 回调都会执行状态重置

### 具体位置：
- 第2375行：SUMMARY_AGENT 的 planSummary 处理中
- 第2195行：onComplete 回调中  
- 第2460行：执行步骤处理完成后

## 修复方案
移除 `SUMMARY_AGENT` 处理逻辑中的重复状态重置，让 `onComplete` 回调统一处理：

### 修改内容：
1. **SUMMARY_AGENT 处理逻辑**：移除 `sending.value = false` 和 `refreshHistory()` 调用
2. **执行步骤处理**：移除 `sending.value = false` 设置
3. **统一状态管理**：让 onComplete 回调统一处理状态重置和历史刷新

## 修改的文件
- `src/views/chatagent/pages/chat/index.vue`

## 验证方法
1. 打开聊天界面
2. 发送第一条消息，等待回复完成
3. 发送第二条消息，观察是否只显示一次
4. 继续多轮对话，确认消息不会重复显示

## 预期效果
- 每条消息只显示一次
- 对话状态正确更新
- 历史记录正常刷新
- 发送状态正确重置