<template>
  <div class="knowledge-management-container">
    <!-- 左侧导航 -->
    <div class="sidebar">
      <h2 class="sidebar-title">知识管理</h2>
      <div class="divider"></div>
      <div class="tab-list">
        <div 
          v-for="tab in tabs" 
          :key="tab.value"
          class="tab-item"
          :class="{ active: activeTab === tab.value }"
          @click="activeTab = tab.value"
        >
          {{ tab.label }}
        </div>
      </div>
    </div>

    <!-- 右侧内容区域 -->
    <div class="content">
      <!-- 名词解释 -->
      <div v-if="activeTab === 'term'" class="content-section">
        <div class="section-header">
          <h2>名词解释</h2>
          <p class="description">请协助 ChatBI 更准确地把握您在数据挖掘过程中的目标描述。您可以在此录入特定的名词。</p>
          <div class="example-tip">
            <el-icon><info-filled /></el-icon>
            <span>名词名称：营业收入，名词解释：企业在日常经营活动中，通过销售商品、提供劳务等方式取得的收入总额</span>
          </div>
        </div>
        <TermTable />
      </div>

      <!-- 业务逻辑解释 -->
      <div v-if="activeTab === 'logic'" class="content-section">
        <div class="section-header">
          <h2>业务逻辑解释</h2>
          <p class="description">通过业务逻辑解释，可以帮助用户理解复杂的业务流程和规则，提高业务理解能力。</p>
          <div class="example-tip">
            <el-icon><info-filled /></el-icon>
            <span>例如：可以添加销售业绩计算方法、库存周转率统计规则、客户信用评级标准等业务逻辑的详细解释。</span>
          </div>
        </div>
        <BusinessLogicTable />
      </div>

      <!-- 案例优化 -->
      <div v-if="activeTab === 'case'" class="content-section">
        <div class="section-header">
          <h2>案例优化</h2>
          <p class="description">通过案例优化，可以记录和分享业务优化的成功案例，促进经验共享和持续改进。</p>
          <div class="example-tip">
            <el-icon><info-filled /></el-icon>
            <span>案例优化：统计最近 7 天内，每天有登录行为的活跃用户数量; 案例优化描述:
SELECT DATE(last_login_time) AS login_date,
       COUNT(DISTINCT user_id) AS active_users_count
FROM user_activity
WHERE last_login_time >= CURDATE() - INTERVAL 7 DAY
GROUP BY DATE(last_login_time)
ORDER BY login_date;</span>
          </div>
        </div>
        <CaseOptimizationTable />
      </div>
      <!-- 数据标注 -->
      <div v-if="activeTab === 'annotation'" class="content-section">
        <div class="section-header">
          <h2>数据标注</h2>
          <p class="description">通过数据标注，可以为数据表字段添加业务含义和注释，提高数据理解和使用效率。</p>
          <div class="example-tip">
            <el-icon><info-filled /></el-icon>
            <span>例如：可以为用户表的字段添加中文名称和业务含义，如 user_id -> 用户ID，create_time -> 创建时间等。</span>
          </div>
        </div>
        <DataAnnotationTable />
      </div>

    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { InfoFilled } from '@element-plus/icons-vue';
import TermTable from './TermTable.vue';
import BusinessLogicTable from './BusinessLogicTable.vue';
import CaseOptimizationTable from './CaseOptimizationTable.vue';
import DataAnnotationTable from './DataAnnotationTable.vue';
// import { useTheme } from '@chat/themechange';

// 使用主题composable
// const { defaultColor, changeTheme, initTheme } = useTheme();

// 定义标签页
const tabs = [
  { label: '名词解释', value: 'term' },
  { label: '业务逻辑解释', value: 'logic' },
  { label: '案例优化', value: 'case' },
  { label: '数据标注', value: 'annotation' }
];

// 当前激活的标签页
const activeTab = ref('term');

// 主题色
// const primaryColor = ref(defaultColor.value);

// onMounted(() => {
//   // 监听微前端主题变更事件
//   window?.$wujie?.bus.$on('wujieTheme', (color: string) => {
//     console.log('收到主应用的主题颜色：', color);
//     primaryColor.value = color;
//     changeTheme(color);
//   });
  
//   // 初始化主题
//   initTheme();
// });
</script>

<style scoped lang="scss">
.knowledge-management-container {
  display: flex;
  height: calc(100vh - 62px);
  background-color: #fff;
  border-radius: 8px;
  border: 1px solid #e6e8ee;
  margin-right: 8px;
}

.sidebar {
  width: 200px;
  background-color: #fff;
  border-right: 1px solid #e6e6e6;
  padding: 20px 0;
  display: flex;
  flex-direction: column;
  border-top-left-radius: 8px;
  border-bottom-left-radius: 8px;
}

.sidebar-title {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
  padding: 0 20px;
  margin: 0 0 15px 0;
}

.divider {
  height: 1px;
  background-color: #ebeef5;
  margin-bottom: 15px;
}

.tab-list {
  display: flex;
  flex-direction: column;
}

.tab-item {
  padding: 12px 20px;
  cursor: pointer;
  color: #606266;
  transition: all 0.3s;
  font-size: 14px;

  &:hover {
    background-color: #f5f7fa;
  }

  &.active {
    color: #409eff;
    background-color: #ecf5ff;
    border-right: 3px solid #409eff;
  }
}

.content {
  flex: 1;
  // padding: 20px;
  overflow-y: auto;
}

.content-section {
  background-color: #fff;
  border-radius: 8px;
  // box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  padding: 20px 20px 0 20px;
  height: calc(100vh - 64px);
}

.section-header {
  margin-bottom: 0px;
}

.section-header h2 {
  font-size: 17px;
  font-weight: bold;
  color: #303133;
  margin: 0 0 10px 0;
}

.description {
  font-size: 14px;
  color: #606266;
  margin: 0 0 15px 0;
  line-height: 1.5;
}

.example-tip {
  background-color: #f0f9eb;
  border-radius: 4px;
  padding: 10px 15px;
  display: flex;
  align-items: flex-start;
  // margin-bottom: 20px;
  
  .el-icon {
    color: #67c23a;
    margin-right: 8px;
    margin-top: 2px;
  }
  
  span {
    font-size: 14px;
    color: #606266;
    line-height: 1.5;
  }
}
</style>