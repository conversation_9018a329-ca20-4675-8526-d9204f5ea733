# 开发环境
NODE_ENV=development
# 配置优先走mock数据还是后端代理
VITE_MOCK=false
# 是否子应用
VITE_MICRO=false

VITE_PORT=5000

# 接口基础请求地址  通常配置如：http://localhost:9000
VITE_APP_BASE_URL=
# 与VITE_APP_BASE_URL配合 通常配置 如： /api => http://localhost:9000/api
# VITE_APP_BASE_URL_PREFIX=
VITE_APP_BASE_URL_PREFIX=/prechat
# 代理  http://***********:9090 ***********
# VITE_PROXY = [["/prechat/system","http://************:18080/system"],["/prechat/sys","http://************:18080"],["/prechat/cpit","http://************:18080/cpit"],["/prechat/api","http://************:18080/api"],["/prechat/key","http://************:18080"]]
# VITE_PROXY = [["/prechat/system","http://***********:18080/system"],["/prechat/sys","http://***********:18080"],["/prechat/cpit","http://***********:18080/cpit"],["/prechat/api","http://***********:18080/api"]]
# VITE_PROXY = [["/system","http://************:18080/system"],["/sys","http://***********:18080"],["/cpit","http://***********:18080/cpit"],["/api","http://***********:18080/api"]]
VITE_PROXY = [["/prechat/system","http://************:18081/system"],["/prechat/sys","http://************:18081"],["/prechat/cpit","http://************:18081/cpit"],["/prechat/api","http://************:18081/api"],["/prechat/key","http://************:18081"]]
# 静态资源地址()
VITE_APP_STATIC_URL=

# 构建资源公共路径
VITE_PUBLIC_PATH=/

# 统一认证登录页面地址
VITE_LOGIN_PAGE=http://**************:18001

# 项目部署后的地址
VITE_PROJECT_BASE_URL=localhost:8080
