<template>
  <div class="sql-editor-container">
    <!-- 工具栏 -->
    <div class="editor-toolbar">
      <div class="toolbar-left">
        <el-button-group>
          <el-button type="primary" size="small" @click="executeSqlFn">
            <!-- <el-icon><VideoPlay /></el-icon> -->
            执行
          </el-button>
          <!-- <el-button size="small" @click="formatSql">
            <el-icon><Operation /></el-icon>
            格式化
          </el-button> -->
        </el-button-group>
      </div>
    </div>
    
  <!-- Prism.js 只读高亮编辑器 -->
  <pre class="language-sql" style="flex:1;min-height:300px;"><code ref="codeBlock">{{ props.value }}</code></pre>
    
    <!-- SQL执行结果 -->
    <div v-if="showResult" class="sql-result-container">
      <div v-if="loading" class="sql-loading">
        <el-icon class="loading"><Loading /></el-icon> 正在执行...
      </div>
      <div v-else-if="error" class="sql-error">
        查询失败
      </div>
      <template v-else>
        <el-tabs v-model="activeTab">
          <!-- <el-tab-pane label="总览" name="summary">
            <div class="sql-summary">
              执行成功: {{ result.time || 0 }}ms, {{ result.rows || 0 }}行
            </div>
          </el-tab-pane> -->
          <el-tab-pane label="结果" name="result">
            <div v-if="tableData.length >0 && tableColumns.length > 0">
              <VTable 
                :data="tableData" 
                :columns="tableColumns"
              />
            </div>
            <div v-else class="sql-empty">查询结果为空</div>
          </el-tab-pane>
        </el-tabs>
      </template>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { VideoPlay, Operation, DocumentAdd, Download, DataLine, Loading } from '@element-plus/icons-vue';
import Prism from 'prismjs';
import 'prismjs/themes/prism.css';
import 'prismjs/components/prism-sql';
import VTable from './VTable.vue';
// 引入api
import { executeSql } from '/@/views/team/api/index';
// Prism高亮引用
const codeBlock = ref(null);

// 数据源相关
const selectedDataSource = ref('');
const selectedDatabase = ref('');
const selectedSchema = ref('');
const databases = ref([]);
const schemas = ref([]);

// SQL执行结果相关
const showResult = ref(false);
const loading = ref(false);
const error = ref(null);
const activeTab = ref('result');
const tableColumns = ref([]);
const tableData = ref([]);

// 定义props
const props = defineProps({
  value: {
    type: String,
    default: ''
  },
  dataSourceId: {
    type: Number,
    default: 0
  },
  databaseName: {
    type: String,
    default: ''
  },
  schemaName: {
    type: String,
    default: ''
  },
  refresh: {
    type: Boolean,
    default: true
  }
});

// Prism高亮初始化
onMounted(() => {
  if (codeBlock.value) {
    Prism.highlightElement(codeBlock.value);
  }
});

// 监听value属性变化，重新高亮
watch(() => props.value, () => {
  if (codeBlock.value) {
    Prism.highlightElement(codeBlock.value);
  }
});

// Prism只读，无需销毁处理

// 执行SQL
const executeSqlFn = async () => {
  // 只读高亮场景，直接用props.value作为SQL
  const sql = props.value;
  
  if (!sql.trim()) {
    ElMessage.warning('SQL语句不能为空');
    return;
  }
  // ...existing code...
  showResult.value = true;
  loading.value = true;
  error.value = null;
  try {
    const params = {
      dataSourceId: props.dataSourceId,
      databaseName: props.databaseName,
      schemaName: props.schemaName,
      refresh: props.refresh,
      sql: sql
    };
    const response = await executeSql(params);
    
    // ...existing code...
    if (response && response.length > 0) {
      tableColumns.value = [];
      const headerList = response[0].headerList || [];
      const dataList = response[0].dataList || [];
      
      if (headerList && headerList.length > 0) {
        headerList.forEach(header => {
          if (header.name) {
            tableColumns.value.push(header.name);
          }
        });
        dataList.forEach(item => {
          const rowData = {};
          headerList.forEach((header, index) => {
            if (header.name) {
              rowData[header.name] = item[index];
            }
          });
          tableData.value.push(rowData);
        });
      }
    }
    loading.value = false;
  } catch (err) {
    error.value = err.toString();
    loading.value = false;
  }
};

// 格式化SQL（只读高亮场景，无法修改内容）
const formatSql = () => {
  ElMessage.info('只读模式无法格式化SQL');
};

// 保存SQL（只读高亮场景，无法修改内容）
const saveSql = () => {
  ElMessage.info('只读模式无法保存SQL');
};

// 另存为文件（只读高亮场景，直接用props.value）
const saveAsFile = () => {
  const sql = props.value;
  if (!sql.trim()) {
    ElMessage.warning('SQL语句不能为空');
    return;
  }
  const blob = new Blob([sql], { type: 'text/plain' });
  const link = document.createElement('a');
  link.href = URL.createObjectURL(blob);
  link.download = `query_${new Date().getTime()}.sql`;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(link.href);
  ElMessage.success('SQL文件已下载');
};

// 数据源变更处理
const onDataSourceChange = () => {
  // 重置数据库和schema选择
  selectedDatabase.value = '';
  selectedSchema.value = '';
  schemas.value = [];
  
  // 模拟加载数据库列表
  databases.value = [
    { label: 'MySQL数据库', value: 'mysql' },
    { label: 'PostgreSQL数据库', value: 'postgres' },
    { label: 'Oracle数据库', value: 'oracle' }
  ];
};

// 数据库变更处理
const onDatabaseChange = () => {
  // 重置schema选择
  selectedSchema.value = '';
  
  // 模拟加载schema列表
  schemas.value = [
    { label: 'public', value: 'public' },
    { label: 'dbo', value: 'dbo' },
    { label: 'information_schema', value: 'information_schema' }
  ];
};
</script>

<style scoped>
.sql-editor-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  border: 1px solid #e6e6e6;
  border-radius: 4px;
  overflow: hidden;
}

.editor-toolbar {
  display: flex;
  justify-content: space-between;
  padding: 8px;
  background-color: #e7e7e7;
  /* border-bottom: 1px solid #e6e6e6; */
}

.toolbar-left {
  display: flex;
  gap: 8px;
}

.toolbar-right {
  display: flex;
  gap: 8px;
}

pre.language-sql {
  flex: 1;
  min-height: 300px;
  margin: 0;
  padding: 12px;
  background: #f8f8f8;
  border-radius: 4px;
  font-size: 14px;
  overflow: auto;
}

.sql-result-container {
  margin-top: 10px;
  border-top: 1px solid #e6e6e6;
  max-height: 300px;
  overflow: auto;
  padding: 8px;
}

.sql-loading {
  padding: 16px;
  text-align: center;
  color: #909399;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.sql-error {
  padding: 16px;
  color: #f56c6c;
  background-color: #fef0f0;
}

.sql-summary {
  padding: 16px;
  color: #67c23a;
  background-color: #f0f9eb;
}

.sql-empty {
  padding: 16px;
  text-align: center;
  color: #909399;
}
</style>