const phoneReg =
  /^((\+86|0086)?\s*)((134[0-8]\d{7})|(((13([0-3]|[5-9]))|(14[5-9])|15([0-3]|[5-9])|(16(2|[5-7]))|17([0-3]|[5-8])|18[0-9]|19(1|[8-9]))\d{8})|(14(0|1|4)0\d{7})|(1740([0-5]|[6-9]|[10-12])\d{7}))$/;
const numReg = /^(.*\d{8,}.*$)/;
const passwordReg = /^(?:(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])(?=.*[^A-Za-z0-9])).{12,32}$/;
const emailReg = /^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/;
export const columns: any[] = [
  {
    label: '操作人',
    prop: 'username',
    width: 80,
    ellipsis: true,
    align: 'center',
  },
  {
    label: '操作类型',
    prop: 'businessType',
    width: 120,
    align: 'center',
    slot: 'businessType',
    ellipsis: true,
  },
  {
    label: '请求类型',
    prop: 'requestMethod',
    width: 100,
    ellipsis: true,
    slot: 'requestMethod',
    align: 'center',
  },
  {
    label: '操作记录',
    prop: 'title',
    align: 'center',
    width: 300,
    ellipsis: true,
  },
  // {
  //   label: '操作系统',
  //   prop: 'os',
  //   width: 280,
  //   align: 'center',
  //   ellipsis: true,
  // },
  {
    label: '请求地址',
    prop: 'operUrl',
    minWidth: 200,
    align: 'center',
    ellipsis: true,
  },
  // {
  //   label: '客户端',
  //   prop: 'browser',
  //   width: 170,
  //   align: 'center',
  //   ellipsis: true,
  // },
  {
    label: 'IP',
    prop: 'operIp',
    width: 120,
    align: 'center',
    ellipsis: true,
  },
  // {
  //   label: '耗时(ms)',
  //   prop: 'time',
  //   align: 'center',
  //   width: 100,
  // },
  {
    label: '时间',
    prop: 'operTime',
    align: 'center',
    width: 170,
  },
  {
    label: '操作',
    prop: 'action',
    action: true,
    fixed: 'right',
    align: 'center',
    width: 80,
  },
];
export const searchArray: any[] = [
  {
    field: 'userName',
    label: '操作人',
    component: 'Input',
    placeholder: '请输入操作人',
    colProps: { span: 8 },
    componentProps: {
      clearable: true,
    },
    span: 4,
    labelWidth: 80,
  },
  {
    field: 'businessType',
    label: '操作类型',
    component: 'Select',
    placeholder: '请选择操作类型',
    colProps: { span: 8 },
    span: 4,
    labelWidth: 80,
    componentProps: {
      options: [
        {
          label: '新增',
          value: 1,
        },
        {
          label: '修改',
          value: 2,
        },
        {
          label: '删除',
          value: 3,
        },
        {
          label: '其他',
          value: 0,
        },
      ],
      clearable: true,
    },
  },

  {
    field: 'requestMethod',
    label: '请求类型',
    component: 'Select',
    placeholder: '请选择请求类型',
    colProps: { span: 8 },
    span: 4,
    labelWidth: 80,
    componentProps: {
      options: [
        {
          label: '全部',
          value: '',
        },
        {
          label: 'GET',
          value: 'GET',
        },
        {
          label: 'POST',
          value: 'POST',
        },
        {
          label: 'PUT',
          value: 'PUT',
        },
        {
          label: 'DELETE',
          value: 'DELETE',
        },
      ],
      clearable: true,
    },
  },
  {
    field: 'time',
    label: '时间',
    component: 'DateRangePicker',
    colProps: { span: 8 },
    span: 8,
    labelWidth: 80,
    componentProps: {
      clearable: true,
    },
  },
];

export const accountFormSchema: any[] = [
  {
    field: 'deptName',
    label: '所属机构：',
    component: 'Input',
    componentProps: {
      placeholder: '请输入所属机构',
      disabled: true,
    },
    required: true,
  },
  {
    field: 'username',
    label: '用户名：',
    component: 'Input',
    componentProps: {
      placeholder: '请输入用户名',
    },
    required: true,
  },
  {
    field: 'nickName',
    label: '用户名称：',
    component: 'Input',
    componentProps: {
      placeholder: '请输入用户名称',
    },
    required: true,
  },
  {
    field: 'password',
    label: '登录密码：',
    component: 'InputPassword',
    componentProps: {
      placeholder: '请输入新密码',
    },
    dynamicRules: () => {
      return [
        {
          required: true,
          validator: (_, value) => {
            if (numReg.test(value)) {
              return Promise.reject('密码不能存在8位连续数字！');
            }
            if (!passwordReg.test(value)) {
              return Promise.reject('长度12到32位，包含大小写、数字、特殊字符！');
            }
            return Promise.resolve();
          },
        },
      ];
    },
    ifShow: false,
    required: true,
  },
  {
    label: '电子邮箱：',
    field: 'email',
    component: 'Input',
    componentProps: {
      placeholder: '请输入电子邮箱',
    },
    rules: [{ required: true, pattern: emailReg, message: '请输入正确的电子邮箱' }],
    required: true,
  },
  {
    label: '手机号：',
    field: 'phone',
    component: 'Input',
    componentProps: {
      placeholder: '请输入手机号',
    },
    rules: [{ required: true, pattern: phoneReg, message: '请输入正确的手机号' }],
    required: true,
  },
  {
    field: 'description',
    label: '用户简介：',
    component: 'Input',
    componentProps: {
      placeholder: '请输入用户简介',
    },
  },

  {
    field: 'roleIdList',
    label: '用户角色：',
    component: 'Select',
    componentProps: {
      placeholder: '请选择用户角色',
      mode: 'multiple',
      allowClear: false,
    },
  },
  {
    label: '用户状态：',
    field: 'available',
    component: 'RadioGroup',
    componentProps: {
      options: [
        {
          label: '已激活',
          value: '1',
        },
        {
          label: '已禁用',
          value: '0',
        },
      ],
    },
    required: true,
  },
];

export const passwordFormSchema: any[] = [
  {
    field: 'username',
    label: '用户名：',
    component: 'Input',
    componentProps: {
      disabled: true,
    },
    required: true,
  },
  {
    label: '电子邮箱：',
    field: 'sendEmail',
    component: 'CheckboxGroup',
    componentProps: {
      options: [
        {
          label: '发送邮件通知：',
          value: true,
        },
      ],
    },
  },
  {
    label: '手机号：',
    field: 'sendSMS',
    component: 'CheckboxGroup',
    componentProps: {
      options: [
        {
          label: '发送短信通知：',
          value: true,
        },
      ],
    },
  },
  {
    label: '',
    field: 'defaultPsw',
    component: 'CheckboxGroup',
    slot: 'defaultPassword',
  },
];

export const descriptionLabel: any[] = [
  {
    field: 'title',
    label: '标题',
    span: 1,
  },
  {
    field: 'businessType',
    label: '操作类型',
    span: 1,
  },
  {
    field: 'title',
    label: '操作记录',
    span: 1,
  },
  {
    field: 'username',
    label: '操作人',
    span: 1,
  },
  {
    field: 'operIp',
    label: '操作人ip',
    span: 1,
  },
  {
    field: 'operTime',
    label: '操作时间',
    span: 1,
  },
  // {
  //   field: 'browser',
  //   label: '客户端',
  //   span: 1,
  // },
  // {
  //   field: 'os',
  //   label: '操作系统',
  //   span: 1,
  // },
  {
    field: 'operUrl',
    label: '请求地址',
    span: 1,
  },
  {
    field: 'operParam',
    label: '请求参数',
    span: 1,
  },
  {
    field: 'jsonResult',
    label: '返回值',
    span: 1,
  },
  //
  // {
  //   field: 'userAgent',
  //   label: '终端',
  //   span: 1,
  // },
];
