<template>
  <div class="agent-container">
    <div class="main">
      <div class="sider">
        <h2 class="sidebar-title">数据源管理</h2>
        <div class="divider"></div>
        <div class="sider-center">
          <div
            v-for="(item, index) in dataSourceLists"
            :key="item.id"
            class="list"
            @click="selectItem(index)"
            :class="{ active: selectedIndex === index }"
            @contextmenu.prevent="(e) => showItemContextMenu(e, item, index)"
          >
            <div class="list-title">
              <span
                class="annotation"
                :style="{ background: item.environment.color.toLocaleLowerCase() }"
              >
              </span>
              <Iconfont
                style="margin-right: 6px"
                v-if="item.type"
                :name="getIconByLabel(item.type.toLowerCase())"
                :color="item.environment.color.toLocaleLowerCase()"
              />
              <span>{{ item.alias }}</span>
            </div>

            <div>
              <el-icon
                style="margin-right: 8px; color: #409eff"
                size="12"
                @click.stop="cloneDatasource(item.id)"
                ><DocumentCopy
              /></el-icon>

              <el-popconfirm
                :icon="InfoFilled"
                title="确定要删除吗?"
                @confirm="handleDeketeDatasource(item.id)"
              >
                <template #reference>
                  <el-icon
                    @click.stop
                    style="margin-right: 8px; color: #409eff"
                    size="12"
                    ><Delete
                  /></el-icon>
                </template>
              </el-popconfirm>
            </div>
          </div>
        </div>
        <div class="sider-bottom" v-if="status !== 'add'">
          <el-button :icon="Plus" @click="handleAddBtn">添加连接</el-button>
        </div>
      </div>
      <div class="content">
        <div class="content-list" v-if="status === 'add'">
          <el-row :gutter="20">
            <el-col
              v-for="(item, index) in databaseTypeList"
              :key="index"
              :xs="24"
              :sm="24"
              :md="12"
              :lg="8"
              :xl="8"
            >
              <div class="grid-content">
                <!-- 你的内容 -->
                <!-- {{ item }} -->

                <div class="dataBaseList">
                  <div
                    class="databaseItem"
                    :class="{ 'no-active': !item.support }"
                    @click="handleAdd(item)"
                  >
                    <div class="databaseItemMain">
                      <div class="databaseItemLeft"
                        ><Iconfont style="margin-right: 8px" :name="item.icon" />
                        {{ item.label }}
                      </div>
                      <div class="databaseItemRight">
                        <el-icon><Plus /></el-icon>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
        <div v-else>
          <div class="form-content">
            <p>
              <Iconfont
                style="margin-right: 6px"
                :size="24"
                :name="localDataBaseType.icon"
                color="#409eff"
              />
              <span>{{ localDataBaseType.label }}</span>
            </p>
            <!-- :rules="rules" -->
            <el-form
              :model="form"
              label-width="70px"
              label-position="left"
              :rules="rules"
              ref="formRef"
            >
              <el-form-item label="名称" prop="alias">
                <el-input v-model="form.alias" placeholder="请填写名称" />
              </el-form-item>
              <el-form-item label="环境">
                <el-select v-model="form.environmentId" placeholder="请选择环境">
                  <el-option
                    v-for="item in environmentOptions"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  >
                    <span
                      class="annotation"
                      :style="{
                        background: item.color.toLocaleLowerCase(),
                      }"
                    >
                    </span>
                    <span>{{ item.name }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
              <!-- :rules="[
                      { required: true, message: '请填写主机地址', trigger: 'blur' },
                    ]" -->
              <el-row>
                <el-col :span="15">
                  <el-form-item label="主机" prop="host">
                    <el-input
                      v-model="form.host"
                      placeholder="请填写主机地址"
                      @input="(value) => handleInput(value, 'host')"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="2" />
                <!-- :rules="[
                      { required: true, message: '请填写端口号', trigger: 'blur' },
                    ]" -->
                <el-col :span="7">
                  <el-form-item label="端口" prop="port">
                    <el-input
                      v-model="form.port"
                      placeholder="请填写端口号"
                      @input="(value) => handleInput(value, 'port')"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-form-item label="身份验证">
                <el-select
                  v-model="form.authenticationType"
                  placeholder="请选择身份验证"
                >
                  <el-option label="User&Password" value="1" />
                  <el-option label="NONE" value="2" />
                </el-select>
              </el-form-item>
              <el-form-item label="用户名" v-if="form.authenticationType === '1'">
                <el-input v-model="form.user" />
              </el-form-item>
              <el-form-item label="密码" v-if="form.authenticationType === '1'">
                <el-input
                  v-model="form.password"
                  type="password"
                  :show-password="true"
                />
              </el-form-item>
              <el-form-item label="数据库">
                <el-input
                  v-model="form.database"
                  @input="(value) => handleInput(value, 'database')"
                />
              </el-form-item>
              <el-form-item label="URL">
                <el-input v-model="form.url" disabled />
              </el-form-item>

              <el-collapse expand-icon-position="left" v-model="activeNames">
                <el-collapse-item name="1">
                  <template #title>
                    <div class="collapse-header">
                      <div class="collapse-header-left">
                        <span>驱动</span>
                      </div>
                    </div>
                  </template>
                  <div style="padding: 12px">
                    <el-form-item label="驱动">
                      <!-- <el-input v-model="form.driverConfig.jdbcDriver" /> -->

                      <el-select v-model="form.driverConfig.jdbcDriver">
                        <el-option
                          v-for="item in driverConfigOptins"
                          :key="item.jdbcDriver"
                          :label="item.jdbcDriver"
                          :value="item.jdbcDriver"
                        />
                      </el-select>
                    </el-form-item>
                    <el-form-item label="Class">
                      <el-input v-model="form.driverConfig.jdbcDriverClass" disabled />
                    </el-form-item>
                    <el-button type="primary" text @click="openFileVisible">
                      上传
                    </el-button>
                    <!-- " -->
                  </div>
                </el-collapse-item>
                <el-collapse-item name="2">
                  <template #title>
                    <div class="collapse-header">
                      <div class="collapse-header-left">
                        <span>SSH</span>
                      </div>
                    </div>
                  </template>
                  <div style="padding: 12px">
                    <el-form-item label="使用SSH">
                      <el-select v-model="form.ssh.use" placeholder="请选择环境">
                        <el-option label="false" :value="false" />
                        <el-option label="true" :value="true" />
                      </el-select>
                    </el-form-item>
                    <el-row>
                      <el-col :span="15">
                        <el-form-item label="SSH主机">
                          <el-input
                            v-model="form.ssh.hostName"
                            placeholder="请填写主机地址"
                            @input="(value) => handleInputSsh(value, 'host')"
                          />
                        </el-form-item>
                      </el-col>
                      <el-col :span="2" />
                      <el-col :span="7">
                        <el-form-item label="SSH端口">
                          <el-input
                            v-model="form.ssh.port"
                            placeholder="请填写端口号"
                            @input="(value) => handleInputSsh(value, 'port')"
                          />
                        </el-form-item>
                      </el-col>
                    </el-row>
                    <el-row>
                      <el-col :span="15">
                        <el-form-item label="用户名">
                          <el-input v-model="form.ssh.userName" />
                        </el-form-item>
                      </el-col>
                      <el-col :span="2" />
                      <el-col :span="7">
                        <el-form-item label="本地端口">
                          <el-input v-model="form.ssh.localPort" />
                        </el-form-item>
                      </el-col>
                    </el-row>
                    <el-form-item label="身份验证">
                      <el-select
                        v-model="form.ssh.authenticationType"
                        placeholder="请选择身份验证"
                      >
                        <el-option label="Password" value="password" />
                        <el-option label="Private key" value="keyFile" />
                      </el-select>
                    </el-form-item>
                    <el-form-item
                      label="密钥文件"
                      v-if="form.ssh.authenticationType === 'keyFile'"
                    >
                      <el-input
                        v-model="form.ssh.keyFile"
                        placeholder="/user/userName/.ssh/xxxx"
                      />
                    </el-form-item>
                    <el-form-item label="密码">
                      <el-input
                        v-model="form.ssh.password"
                        type="password"
                        :show-password="true"
                      />
                    </el-form-item>
                    <el-button type="primary" text @click="handleSshConnect">
                      测试ssh连接
                    </el-button>
                    <!-- <el-form-item label="URL">
                      <el-input v-model="form.driverConfig.jdbcDriverClass" />
                    </el-form-item> -->
                  </div>
                </el-collapse-item>
              </el-collapse>

              <!-- <el-form-item label="Resources">
                <el-radio-group v-model="form.resource">
                  <el-radio value="Sponsor">Sponsor</el-radio>
                  <el-radio value="Venue">Venue</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="Activity form">
                <el-input v-model="form.desc" type="textarea" />
              </el-form-item>
              <el-form-item>
                <el-button type="primary">Create</el-button>
                <el-button>Cancel</el-button>
              </el-form-item> -->
            </el-form>
            <div class="form-bottom">
              <div>
                <el-button @click="handleConnect">测试链接</el-button>
              </div>
              <div>
                <el-button @click="handleAddBtn">取消</el-button>
                <el-button
                  type="primary"
                  @click="handleCreateDatasource(formRef)"
                  v-if="selectedIndex < 0"
                  :loading="loading"
                  >添加</el-button
                >
                <el-button
                  type="primary"
                  @click="handleUpdateDatasource(formRef)"
                  v-else
                  :loading="loading"
                  >修改</el-button
                >
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <el-dialog
      v-model="fileVisible"
      title="上传"
      width="600px"
      align-center
      destroy-on-close
      class="file-dialog"
    >
      <el-form>
        <el-form-item label="Class">
          <el-input v-model="classField" />
        </el-form-item>
      </el-form>
      <el-upload
        ref="upload"
        class="upload-demo"
        drag
        :limit="1"
        accept=".jar"
        name="multipartFiles"
        :show-file-list="true"
        :auto-upload="true"
        :on-exceed="handleExceed"
        :on-change="handleChange"
        :on-success="handleSuccess"
        :headers="{ Authorization: token }"
        action="/api/jdbc/driver/upload"
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">
          将文件拖拽到此处 <br /><em>或者，您可以单击此处选择一个文件</em>
        </div>
        <template #tip>
          <div class="el-upload__tip" style="color: red">
            注：只支持jar文件类型的文件</div
          >
        </template>
      </el-upload>
      <!-- :disabled="!fileStatus || classField !== ''" -->
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="fileVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="saveUploadFile"
            :loading="uploadLoading"
            :disabled="!fileStatus || classField === ''"
          >
            保存
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import { Plus, Delete, DocumentCopy, InfoFilled } from '@element-plus/icons-vue';
  //   import { cloneDeep } from 'lodash-es';  InfoFilled
  import '/@/assets/font/iconfont.css'; // 本地文件
  import Iconfont from '/@/components/Iconfont.vue';
  import type { UploadInstance, FormInstance, FormRules } from 'element-plus';
  // import type { FormInstance, FormRules } from 'element-plus';
  // import type { FormInstance, FormRules } from 'element-plus'
  import {
    getDatasourceApi,
    getEnvironmentApi,
    getDriverListApi,
    createDatasourceApi,
    updateDatasourceApi,
    deleteDatasourceApi,
    cloneDatasourceApi,
    sshConnectApi,
    datasourceConnectApi,
    getByIdAiSourceApi,
    driverSaveApi,
  } from '../../api/index';
  import useUpload from '../../hooks/useUpload';
  import { getAuthToken } from '/@/utils/storage/auth';
  const token = getAuthToken();
  const dataSourceLists = ref<any[]>([]);
  const selectedIndex = ref(-1);
  const status = ref('add');
  const databaseTypeList = ref([
    {
      label: 'MySQL',
      value: 'mysql',
      icon: 'mysql',
      support: true,
    },
    {
      label: 'H2',
      value: 'h2',
      icon: 'h2',
      support: false,
    },
    {
      label: 'Oracle',
      value: 'oracle',
      icon: 'oracle',
      support: false,
    },
    {
      label: 'PostgreSql',
      value: 'postgresql',
      icon: 'postgresql',
      support: true,
    },
    {
      label: 'SQLServer',
      icon: 'sqlserver',
      value: 'sqlserver',
      support: false,
    },
    {
      label: 'SQLite',
      icon: 'sqlite',
      value: 'sqlite',
      support: false,
    },
    {
      label: 'Mariadb',
      value: 'mariadb',
      icon: 'rds_mariadb',
      support: true,
    },
    {
      label: 'ClickHouse',
      value: 'clickhouse',
      icon: 'clickhouse-yunshujukuClickHouse',
      support: true,
    },
    {
      label: 'DM',
      value: 'dm',
      icon: 'dameng1',
      support: false,
    },
    {
      label: 'Presto',
      value: 'presto',
      icon: 'presto_sql',
      support: false,
    },
    {
      label: 'DB2',
      value: 'db2',
      icon: 'shujukuleixingtubiao-kuozhan-',
      support: false,
    },
    {
      label: 'OceanBase',
      icon: 'oceanbase',
      value: 'oceanbase',
      support: false,
    },
    {
      label: 'Hive',
      icon: 'HIVE',
      value: 'hive',
      support: true,
    },
    {
      label: 'KingBase',
      icon: 'Kingbase',
      value: 'kingbase',
      support: true,
    },
    {
      label: 'MongoDB',
      icon: 'mongodb',
      value: 'mongodb',
      support: true,
    },

    {
      label: 'Timeplus',
      value: 'timeplus',
      icon: 'clickhouse-yunshujukuClickHouse',
      support: false,
    },
  ]);
  const environmentOptions = ref<any[]>([]);
  const driverConfigOptins = ref<any[]>([]);
  const form = ref<any>({
    alias: '@localhost',
    environmentId: null,
    host: '',
    port: '',
    authenticationType: '1', //身份验证   1是用户名加密码
    user: 'root',
    password: '',
    database: '',
    url: '',
    type: '',
    connectionEnvType: 'DAILY',
    driverConfig: {
      jdbcDriver: '',
      jdbcDriverClass: '',
    },
    ssh: {
      use: false,
      port: '22',
      hostName: '',
      localPort: '',
      userName: '',
      authenticationType: 'password',
      password: '',
      keyFile: '',
    },
    extendInfo: [
      { key: 'zeroDateTimeBehavior', value: 'convertToNull' },
      { key: 'useInformationSchema', value: 'true' },
      { key: 'tinyInt1isBit', value: 'false' },
    ],
  });
  const activeNames = ref(['1']);
  const localDataBaseType = ref<any>({});
  const upload = ref<UploadInstance>();
  const uploadLoading = ref(false);
  const loading = ref(false);
  const classField = ref('');
  const formRef = ref();

  const rules = ref<FormRules>({
    alias: [{ required: true, message: '请填写名称', trigger: 'blur' }],
    host: [{ required: true, message: '请填写主机', trigger: 'blur' }],
    port: [{ required: true, message: '请填写端口', trigger: 'blur' }],
  });

  const {
    fileStatus,
    fileVisible,
    fileName,
    handleSuccess,
    handleExceed,
    handleChange,
  } = useUpload(upload);
  const getList = async (data = -1) => {
    const res = await getDatasourceApi();
    dataSourceLists.value = res.data;
    if (data >= 0) {
      selectedIndex.value = findIndexById(dataSourceLists.value, data);
      status.value = 'edit';
      // form.value = dataSourceLists.value[selectedIndex.value];
      getByIdAiSource(dataSourceLists.value[selectedIndex.value].id);
      // getDriverList({ dbType: form.value.type.toUpperCase() });
    }
  };
  const findIndexById = (arr, id) => {
    return arr.findIndex((item) => item.id === id);
  };
  getList();

  const getEnvironmentOptions = async () => {
    const res = await getEnvironmentApi();
    environmentOptions.value = res;
  };
  getEnvironmentOptions();
  const getIconByLabel = (label: string): string | undefined => {
    const foundItem = databaseTypeList.value.find((item) => item.value === label);
    return foundItem ? foundItem.icon : undefined;
  };
  const selectItem = (index) => {
    selectedIndex.value = index; // 更新选中的索引
    status.value = 'edit';
    const typeIndex = databaseTypeList.value.findIndex(
      (item) => item.value === dataSourceLists.value[index].type.toLowerCase(),
    );
    localDataBaseType.value = databaseTypeList.value[typeIndex];
    getByIdAiSource(dataSourceLists.value[index].id);

    formRef.value.resetFields();
  };

  const handleAdd = (item) => {
    if (item.support) {
      status.value = 'edit';
      localDataBaseType.value = item;
      form.value = {
        alias: '@localhost',
        environmentId: null,
        host: '',
        port: '',
        authenticationType: '1', //身份验证   1是用户名加密码
        user: 'root',
        password: '',
        database: '',
        url: '',
        type: item.label.toUpperCase(),
        connectionEnvType: 'DAILY',
        driverConfig: {
          jdbcDriver: '',
          jdbcDriverClass: '',
        },
        ssh: {
          use: false,
          port: '22',
          hostName: '',
          localPort: '',
          userName: '',
          authenticationType: 'password',
          password: '',
          keyFile: '',
        },
        extendInfo: [
          { key: 'zeroDateTimeBehavior', value: 'convertToNull' },
          { key: 'useInformationSchema', value: 'true' },
          { key: 'tinyInt1isBit', value: 'false' },
        ],
      };
      getDriverList({ dbType: item.label.toUpperCase() });
    } else {
      ElMessage({
        type: 'warning',
        message: `正在开发`,
      });
    }
  };

  const getDriverList = async (params, flag = false) => {
    const res = await getDriverListApi(params);

    if (res.driverConfigList && res.driverConfigList.length > 0) {
      form.value.driverConfig.jdbcDriver = res.driverConfigList[0].jdbcDriver;
      form.value.driverConfig.jdbcDriverClass = res.driverConfigList[0].jdbcDriverClass;
      driverConfigOptins.value = res.driverConfigList;
      classField.value = res.driverConfigList[0].jdbcDriverClass;
    } else {
      driverConfigOptins.value = [];
      classField.value = res.defaultDriverConfig.jdbcDriverClass;
    }
    if (!flag) {
      form.value.url = res.defaultDriverConfig.url;
      const urlArray = extractMySQLComponents(form.value.url);
      form.value.host = urlArray[0];
      form.value.port = urlArray[1];
      form.value.database = urlArray[2];
      form.value.url = res.defaultDriverConfig.url;
    }
  };
  const handleAddBtn = () => {
    status.value = 'add';
    selectedIndex.value = -1;
  };

  const extractMySQLComponents = (url) => {
    const regex = /\/\/([^/:]+)(?::(\d+))?(?:\/([^/]*))?\/?/;
    const match = url.match(regex);

    if (!match) {
      return ['localhost', '3306', '']; // 默认值
    }

    return [
      match[1], // host
      match[2] || '3306', // port (默认3306)
      match[3] || '', // database (默认为空字符串)
    ];
  };

  const handleInput = (value, type) => {
    const protocol = form.value.url.substring(0, form.value.url.indexOf('//') + 2);
    const host = form.value.url.split('//')[1].split(':')[0].trim();
    const portMatch = form.value.url.match(/:\/\/(?:[^:]+:)?(\d+)/);
    console.log(portMatch);
    const port = portMatch ? portMatch[1].replace(/\D/g, '') : form.value.port; // 默认3306如果没找到
    const dbMatch = form.value.url.match(/:\d+\/([^/]*)/);
    const dbName = dbMatch ? dbMatch[1].trim() : ''; // 提取并去除两侧空格
    if (type === 'host') {
      value = value.replace(/[:：]/g, '');
      form.value.host = value;
      form.value.url = protocol + value + ':' + port + '/' + dbName;
    } else if (type === 'port') {
      value = value.replace(/\D/g, '');
      form.value.port = value;
      form.value.url = protocol + host + ':' + value + '/' + dbName;
    } else if (type === 'database') {
      value = value.replace(/[:：]/g, '');
      form.value.database = value;
      form.value.url = protocol + host + ':' + port + '/' + value;
    }

    console.log(form.value.url);
  };

  const handleInputSsh = (value, type) => {
    // form.ssh.host
    if (type === 'host') {
      value = value.replace(/[:：]/g, '');
      form.value.ssh.hostName = value;
    } else if (type === 'port') {
      value = value.replace(/\D/g, '');
      form.value.ssh.port = value;
    }
  };

  const handleCreateDatasource = async (formEl: FormInstance | undefined) => {
    if (!formEl) return;
    await formEl.validate((valid) => {
      if (valid) {
        // console.log('submit!');
        if (!form.value.environmentId) {
          form.value.environmentId = 1;
        }
        loading.value = true;
        createDatasourceApi(form.value)
          .then((res) => {
            console.log(res);
            if (res) {
              getList(res);
              loading.value = false;
              ElMessage({
                type: 'success',
                message: `添加成功`,
              });
            }
          })
          .catch(() => {
            loading.value = false;
          });
      }
    });

    // createDatasourceApi
  };

  const handleUpdateDatasource = async (formEl) => {
    if (!formEl) return;
    await formEl.validate((valid) => {
      if (valid) {
        if (!form.value.environmentId) {
          form.value.environmentId = 1;
        }
        loading.value = true;
        updateDatasourceApi(form.value)
          .then((res) => {
            console.log(res);
            if (res) {
              getList(res);
              loading.value = false;
              ElMessage({
                type: 'success',
                message: `修改成功`,
              });
            }
          })
          .catch(() => {
            loading.value = false;
          });
      }
    });
  };

  const handleDeketeDatasource = (id) => {
    deleteDatasourceApi(id).then(() => {
      status.value = 'add';
      selectedIndex.value = -1;
      getList();
    });
  };

  const cloneDatasource = (id) => {
    cloneDatasourceApi({ id }).then((res) => {
      getList(res);
    });
  };

  const handleSshConnect = () => {
    sshConnectApi(form.value.ssh).then(() => {
      ElMessage({
        type: 'success',
        message: `连接成功`,
      });
    });
  };
  const handleConnect = () => {
    datasourceConnectApi(form.value).then(() => {
      ElMessage({
        type: 'success',
        message: `连接成功`,
      });
    });
  };

  const getByIdAiSource = (id) => {
    getByIdAiSourceApi(id).then((res) => {
      form.value = res;
      const urlArray = extractMySQLComponents(form.value.url);
      form.value.database = urlArray[2];

      getDriverList({ dbType: localDataBaseType.value.label.toUpperCase() }, true);
    });
  };
  const openFileVisible = () => {
    fileVisible.value = true;
    fileStatus.value = false;
    // classField.value = form.value.driverConfig.jdbcDriverClass;
  };
  const saveUploadFile = () => {
    uploadLoading.value = true;
    const params = {
      dbType: form.value.type,
      jdbcDriver: [fileName.value],
      jdbcDriverClass: classField.value,
    };
    console.log(params);
    driverSaveApi(params)
      .then(() => {
        uploadLoading.value = false;
        fileVisible.value = false;
        ElMessage({
          type: 'success',
          message: '上传成功',
        });
        getDriverList({ dbType: form.value.type }, true);
      })
      .catch(() => {
        uploadLoading.value = false;
      });
  };
</script>

<style scoped lang="scss">
  .agent-container {
    height: calc(100vh - 62px);
    border: 1px solid #e6e8ee;
    background-color: #fff;
    border-radius: 8px;
    margin: 0 8px 8px 0;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    .agent-header {
      // border-bottom: 1px solid #e6e8ee;
      background: #fff;
      background-color: #ffffff;
      justify-content: space-between;
      align-items: center;
      height: 64px;
      padding: 16px 20px;
      display: flex;
      position: relative;
    }
    .main {
      display: flex;
      flex: 1;
      .sider {
        flex: 0 0 320px;
        background: #ffffff;
        border-right: 1px solid #e6e8ee;
        display: flex;
        flex-direction: column;
        height: calc(100vh - 65px);
        .sider-title {
          line-height: 36px;
          margin-bottom: 12px;
          font-size: 16px;
          height: 36px;
        }
        .sider-center {
          // flex: 1;
          overflow-y: auto;
          height: calc(100vh - 180px);
          .list.active {
            color: #409eff;
            background-color: #ecf5ff;
            border-right: 3px solid #409eff;
          }
          .list {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            // margin: 0 12px;
            // border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s;
            font-size: 14px;
            color: #606266;
            display: flex;
            justify-content: space-between;
            .el-icon {
              font-size: 16px;
              margin-top: 3px;
            }
            &:hover {
              background-color: #f5f7fa;
            }
            .list-title {
              white-space: nowrap; /* 防止文本换行 */
              overflow: hidden; /* 隐藏溢出的内容 */
              text-overflow: ellipsis; /* 显示省略符号来代表被修剪的文本 */
              width: 200px;
            }
          }
        }
        .sider-bottom {
          height: 30px;
          padding: 0 12px;
          .el-button {
            width: 100%;
          }
        }
        .sidebar-title {
          font-size: 18px;
          font-weight: bold;
          color: #303133;
          padding: 0 20px;
          margin: 15px 0 15px 0;
        }

        .divider {
          height: 1px;
          background-color: #ebeef5;
          margin-bottom: 15px;
        }
      }
      .content {
        flex: 1;
        overflow: auto;
        padding: 24px;
        height: calc(100vh - 66px);
        // display: flex;
        // justify-content: center; /* 水平居中 */
        // align-items: center; /* 垂直居中 */
        .model-p {
          margin-bottom: 12px;
        }

        .content-list {
          display: flex;
          flex: 1;
          justify-content: center; /* 水平居中 */
          align-items: center; /* 垂直居中 */
          height: calc(100vh - 180px);
          .el-row {
            display: flex;
            // justify-content: center; /* 水平居中 */
            align-items: center; /* 垂直居中 */
            .dataBaseList {
              display: flex;
              justify-content: space-between;
              flex-wrap: wrap;
              max-width: 800px;
              .databaseItem {
                flex-grow: 1;
                height: 50px;
                width: 210px;
                margin: 10px 20px;
                padding: 0px 16px;
                border-radius: 8px;
                overflow: hidden;
                box-sizing: border-box;
                border: 1px solid rgba(211, 211, 212, 0.4);

                .databaseItemMain {
                  display: flex;
                  justify-content: space-between;
                  align-items: center;
                  height: 50px;
                  border-radius: 8px;
                  .databaseItemLeft {
                    display: flex;
                    align-items: center;
                  }
                  // .databaseItemRight {
                  // }

                  .databaseItemRight {
                    display: none;

                    i {
                      font-size: 16px;
                    }
                  }
                }
                &:hover {
                  // background-color: var(--color-bg-medium);
                  color: #409eff;
                  border: 1px solid #409eff;
                  cursor: pointer;

                  .databaseItemRight {
                    display: block;

                    i {
                      color: var(--color-primary);
                    }
                  }
                }
              }
              .databaseItem.no-active {
                border: 1px solid rgba(211, 211, 212, 0.4);
                background: rgba(211, 211, 212, 0.4);
                // cursor: none;
              }
            }
          }
        }

        .form-content {
          // background: #f0f0f0;
          padding: 20px 120px 20px;
          p {
            text-align: center;
            margin-bottom: 60px;
            font-size: 28px;
          }

          :deep(.el-collapse) {
            border-bottom: none;
            border-top: none;
          }
          :deep(.el-collapse-item) {
            border: 1px solid rgba(211, 211, 212, 0.4);
            border-radius: 8px;
            .el-icon {
              margin-left: 12px;
            }
          }
          :deep(.el-collapse-item__header) {
            background-color: rgba(35, 36, 41, 0.02);
            border-bottom: 1px solid rgba(211, 211, 212, 0.4);
            border-radius: 8px 8px 0 0;
            height: 40px;
          }
          :deep(.el-collapse-item__wrap) {
            border-bottom: none;
            background-color: rgba(35, 36, 41, 0);
          }
          :deep(.el-collapse-item__content) {
            padding-bottom: 0;
          }
          .collapse-header {
            display: flex;
            justify-content: space-between;
          }

          .form-bottom {
            display: flex;
            justify-content: space-between;
            margin-top: 12px;
          }
        }
      }
    }
  }

  .annotation {
    width: 9px;
    height: 9px;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin-right: 9px;
  }
</style>
