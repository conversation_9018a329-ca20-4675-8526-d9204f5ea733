export interface DataPermissionItem {
  id?: number
  name: string
  description: string
  teamId?: number | string
  datasourceId?: number
  databaseName: string
  schemaName: string
  tableName: string
  conditionExpression?: string
  createTime?: string
  createBy?: string
  updateTime?: string
  updateBy?: string
  enable?: string
  permissionQuery?: string
}

export interface RolePermissionItem {
  id?: number
  roleId: number
  permissionId: number
  teamId: number
  createTime?: string
  createBy?: string
  updateTime?: string
  updateBy?: string
}

export interface ApiKeyItem {
  id?: number
  apiKey: string
  teamId: string
  description?: string
  createTime?: string
  createBy?: string
  updateTime?: string
  updateBy?: string
  status?: string
}