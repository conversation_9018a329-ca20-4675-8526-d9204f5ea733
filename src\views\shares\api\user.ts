import { sharesHttp } from '/@/views/shares/common/http';
import { sysHttp } from '/@/views/upms/common/http';
enum Api {
  info = '/system/sysUsers/updateInfo',
  checkUrl = '/employee/check',
  getConf = '/conf/getConf',
  userConf = '/conf/userConf',
  accurate = '/employee/accurate?userPasswd=', //userPasswd={密码}
}

// 修改用户信息
export const updateInfo = (params) => sharesHttp.post({ url: Api.info, params });

export const checkApi = (params) => sharesHttp.get({ url: Api.checkUrl, params });
// 用户配置查询
export const getConf = (params) => sharesHttp.post({ url: Api.getConf, params });
// 用户配置
export const userConf = (params) => sharesHttp.post({ url: Api.userConf, params });
export const accurate = (userPasswd, employeeId) => {
  return sysHttp.get({
    url: `${Api.accurate}${userPasswd}&employeeId=${employeeId}`,
  });
};
