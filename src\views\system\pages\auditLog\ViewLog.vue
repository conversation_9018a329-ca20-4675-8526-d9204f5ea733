<!-- eslint-disable vue/no-mutating-props -->
<template>
  <ul>
    <!-- JSON.stringify(JSON.parse(item.configJson), null, 2) -->
    <li v-for="item in descriptionLabel" :key="item.field">
      <span class="title">{{ item.label }}</span
      ><span
        class="content"
        v-if="item.field === 'jsonResult' || item.field === 'operParam'"
      >
        <!-- {{record[item.field].component}} -->
        <!-- <JsonEditorVue
          class="editor"
          v-model="json(record[item.field])"
          mode="text"
          :readOnly="true"
          :mainMenuBar="false"
          :expanded-on-start="true"
      /> -->
        <pre style="font-size: 12px">{{
          JSON.stringify(JSON.parse(record[item.field]), null, 2)
        }}</pre>
      </span>

      <span class="content" v-else-if="item.field === 'businessType'">
        <h6 v-if="record[item.field] === 1"> 新增 </h6>
        <h6 v-if="record[item.field] === 2"> 修改 </h6>
        <h6 v-if="record[item.field] === 3"> 删除 </h6>
        <h6 v-if="record[item.field] === 0"> 其他 </h6>
      </span>

      <span class="content" v-else>{{ record[item.field] }}</span>
    </li>
  </ul>
</template>
<script lang="ts" setup name="ViewOperateLog">
  import { PropType } from 'vue';
  import { descriptionLabel } from './log.data';
  // import JsonEditorVue from 'json-editor-vue';

  defineProps({
    record: {
      type: Object as PropType<any>,
      default: () => {},
    },
    descriptionLabel,
  });

  // const json = (value) => {
  //   return JSON.stringify(JSON.parse(value), null, 2);
  // };
</script>
<style scoped>
  li {
    width: 100%;
    display: flex;
  }

  .title {
    width: 150px;
    background: #f5f7fa;
    font-weight: 700;
    color: #606266;
    text-align: right;
  }

  span {
    border: 1px solid #ebeef5;
    padding: 8px 11px;
  }

  .content {
    flex: 1;
  }
</style>
