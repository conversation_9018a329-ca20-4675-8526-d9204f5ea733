<template>
  <div class="menu-container">
    <div class="menu-header">
      <h3>菜单管理</h3>
    </div>

    <!-- 搜索区域 -->
    <div class="search-section">
      <el-form :inline="true" :model="searchParams" class="demo-form-inline">
        <el-form-item label="">
          <el-input
            v-model="searchParams.title"
            placeholder="请输入菜单名称"
            clearable
          />
        </el-form-item>
        <el-form-item label="">
          <el-select
            style="width: 120px"
            v-model="searchParams.visible"
            placeholder="请选择状态"
            clearable
          >
            <el-option label="全部状态" value="" />
            <el-option label="显示" value="1" />
            <el-option label="隐藏" value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 操作按钮区域 -->
    <div class="action-section">
      <el-button type="primary" @click="handleAdd" :icon="Plus"> 新增菜单 </el-button>
      <el-button @click="handleRefresh" :icon="Refresh"> 刷新 </el-button>
      <el-button @click="handleExpandAll" :icon="Sort"> 展开/折叠 </el-button>
    </div>

    <!-- 表格区域 -->
    <div class="table-section">
      <el-table
        v-if="tableShow"
        ref="tableRef"
        :data="tableData"
        v-loading="loading"
        row-key="id"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        :default-expand-all="isExpandAll"
        style="width: 100%"
        stripe
        border
        height="calc(100vh - 330px)"
      >
        <el-table-column
          prop="title"
          label="菜单名称"
          min-width="200"
          align="left"
          show-overflow-tooltip
        />
        <el-table-column prop="icon" label="图标" width="60" align="center">
          <template #default="{ row }">
            <el-icon v-if="row.icon" size="16">
              <component :is="row.icon" />
            </el-icon>
          </template>
        </el-table-column>
        <el-table-column prop="sort" label="显示顺序" width="90" align="center" />
        <el-table-column prop="menuType" label="菜单类型" width="90" align="center">
          <template #default="{ row }">
            <el-tag v-if="row.menuType === 'M'" type="primary" size="small"
              >目录</el-tag
            >
            <el-tag v-else-if="row.menuType === 'C'" type="success" size="small"
              >菜单</el-tag
            >
            <el-tag v-else-if="row.menuType === 'F'" type="warning" size="small"
              >按钮</el-tag
            >
            <el-tag v-else type="info" size="small">{{
              row.menuType || '未知'
            }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="roles"
          label="权限标识"
          min-width="150"
          align="center"
          show-overflow-tooltip
        />
        <el-table-column
          prop="path"
          label="路由地址"
          min-width="200"
          align="center"
          show-overflow-tooltip
        />
        <el-table-column prop="visible" label="显示状态" width="90" align="center">
          <template #default="{ row }">
            <el-tag
              v-if="row.visible === 1 || row.visible === '1'"
              type="success"
              size="small"
              >显示</el-tag
            >
            <el-tag
              v-else-if="row.visible === 0 || row.visible === '0'"
              type="danger"
              size="small"
              >隐藏</el-tag
            >
            <el-tag v-else type="info" size="small">未知</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="createTime"
          label="创建时间"
          width="160"
          align="center"
        />
        <el-table-column label="操作" fixed="right" width="200" align="center">
          <template #default="{ row }">
            <el-button type="primary" link size="small" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button type="primary" link size="small" @click="handleAddChild(row)">
              新增
            </el-button>
            <el-button type="danger" link size="small" @click="handleDelete(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 菜单编辑对话框 -->
    <el-drawer
      v-model="dialogVisible"
      :title="dialogTitle"
      size="500px"
      :close-on-click-modal="false"
      destroy-on-close
    >
      <el-form ref="formRef" :model="formData" :rules="formRules" label-width="100px">
        <!-- <el-row :gutter="20">
          <el-col :span="12"> -->
        <el-form-item label="菜单名称" prop="title">
          <el-input v-model="formData.title" placeholder="请输入菜单名称" />
        </el-form-item>
        <!-- </el-col>
          <el-col :span="12"> -->
        <el-form-item label="菜单类型" prop="menuType">
          <el-select v-model="formData.menuType" placeholder="请选择菜单类型">
            <el-option label="目录" value="M" />
            <el-option label="菜单" value="C" />
            <el-option label="按钮" value="F" />
          </el-select>
        </el-form-item>
        <!-- </el-col>
        </el-row> -->
        <!-- <el-row :gutter="20">
          <el-col :span="24"> -->
        <el-form-item label="上级菜单" prop="parentId">
          <el-tree-select
            v-model="formData.parentId"
            :data="menuTreeOptions"
            :props="{ value: 'id', label: 'title', children: 'children' }"
            placeholder="请选择上级菜单"
            check-strictly
            :render-after-expand="false"
            style="width: 100%"
          />
        </el-form-item>
        <!-- </el-col>
        </el-row> -->
        <!-- <el-row :gutter="20">
          <el-col :span="12"> -->
        <el-form-item label="路由名称" prop="name">
          <el-input v-model="formData.name" placeholder="请输入路由名称" />
        </el-form-item>
        <!-- </el-col>
          <el-col :span="12"> -->
        <el-form-item label="显示顺序" prop="sort">
          <el-input-number v-model="formData.sort" :min="0" style="width: 100%" />
        </el-form-item>
        <!-- </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12"> -->
        <el-form-item label="路由地址" prop="path">
          <el-input v-model="formData.path" placeholder="请输入路由地址" />
        </el-form-item>
        <!-- </el-col>
          <el-col :span="12"> -->
        <el-form-item label="菜单图标" prop="icon">
          <!-- <el-input v-model="formData.icon" placeholder="请输入菜单图标" /> -->
          <IconPicker @select="selectIcon" :name="formData.icon" />
        </el-form-item>
        <!-- </el-col>
        </el-row> -->
        <!-- <el-row :gutter="20">
          <el-col :span="12"> -->
        <el-form-item label="组件路径" prop="component">
          <el-input v-model="formData.component" placeholder="请输入组件路径" />
        </el-form-item>
        <!-- </el-col>
          <el-col :span="12"> -->
        <el-form-item label="权限标识" prop="roles">
          <el-input v-model="formData.roles" placeholder="请输入权限标识" />
        </el-form-item>
        <!-- </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12"> -->
        <el-form-item label="显示状态" prop="visible">
          <el-select v-model="formData.visible" placeholder="请选择显示状态">
            <el-option label="显示" :value="1" />
            <el-option label="隐藏" :value="0" />
          </el-select>
        </el-form-item>
        <!-- </el-col>
          <el-col :span="12"> -->
        <!-- <el-form-item label="菜单状态" prop="status">
          <el-select v-model="formData.status" placeholder="请选择菜单状态">
            <el-option label="正常" :value="0" />
            <el-option label="停用" :value="1" />
          </el-select>
        </el-form-item> -->
        <!-- </el-col>
        </el-row> -->
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
          确定
        </el-button>
      </template>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted, nextTick } from 'vue';
  import { ElMessage, ElMessageBox } from 'element-plus';
  import { Plus, Refresh, Sort } from '@element-plus/icons-vue';
  import {
    getMenuList,
    saveMenu,
    updateMenu,
    deleteMenus,
    type MenuInfo,
    type MenuQueryParams
  } from '/@/api/sys/menus';

  // 响应式数据
  const tableRef = ref();
  const formRef = ref();

  const tableData = ref<MenuInfo[]>([]);
  const loading = ref(false);
  const submitLoading = ref(false);
  const isExpandAll = ref(false);
  const tableShow = ref(true);
  const menuTreeOptions = ref<MenuInfo[]>([]);
  // let icon = ref('');
  // 对话框状态
  const dialogVisible = ref(false);
  const dialogTitle = ref('');
  const isEdit = ref(false);

  // 表单数据
  const formData = ref<MenuInfo>({
    title: '',
    name: '',
    path: '',
    component: '',
    icon: '',
    sort: 0,
    menuType: 'M',
    visible: '1',
    status: '0',
    roles: '',
    parentId: 0,
  });

  // 表单验证规则
  const formRules = {
    title: [
      { required: true, message: '请输入菜单名称', trigger: 'blur' }
    ],
    menuType: [
      { required: true, message: '请选择菜单类型', trigger: 'change' }
    ],
    name: [
      { message: '请输入路由名称', trigger: 'blur' }
    ],
    path: [
      { message: '请输入路由地址', trigger: 'blur' }
    ],
    visible: [
      { required: true, message: '请选择显示状态', trigger: 'change' }
    ],
    status: [
      { required: true, message: '请选择菜单状态', trigger: 'change' }
    ]
  };

  // 搜索参数
  const searchParams = ref<MenuQueryParams>({});

  // 树形数据转换函数
  const handleMenuTree = (data: MenuInfo[]): MenuInfo[] => {
    if (!data || !Array.isArray(data)) {
      return [];
    }

    const childrenMap: { [key: string]: MenuInfo[] } = {};

    // 构建子节点映射
    for (const item of data) {
      const pid = String(item.parentId || 0);
      if (!childrenMap[pid]) {
        childrenMap[pid] = [];
      }
      childrenMap[pid].push(item);
    }

    // 递归构建树形结构
    const buildTree = (pid: string): MenuInfo[] => {
      const children = childrenMap[pid] || [];
      return children.map(item => {
        const node = { ...item };
        const childNodes = buildTree(String(item.id || ''));
        if (childNodes.length > 0) {
          node.children = childNodes;
        }
        return node;
      });
    };

    // 从根节点开始构建（parentId为0或null的节点）
    return buildTree('0');
  };

  // 生命周期
  onMounted(() => {
    loadMenuList();
  });

  // 加载菜单列表
  const loadMenuList = async () => {
    try {
      loading.value = true;
      const result = await getMenuList(searchParams.value);
      if (result) {
        // 将平铺数据转换为树形结构
        tableData.value = handleMenuTree(result);
      }
    } catch (error) {
      ElMessage.error('加载菜单列表失败');
      console.error('Load menu list error:', error);
    } finally {
      loading.value = false;
    }
  };
  function selectIcon(val) {
    formData.value.icon = val;
  }
  // 搜索处理
  const handleSearch = () => {
    loadMenuList();
  };

  // 重置搜索
  const handleReset = () => {
    searchParams.value = {};
    loadMenuList();
  };

  // 刷新
  const handleRefresh = () => {
    loadMenuList();
  };

  // 展开/折叠
  const handleExpandAll = () => {
    isExpandAll.value = !isExpandAll.value;
    // 重新渲染表格以应用展开/折叠状态
    tableShow.value = false;
    nextTick(() => {
      tableShow.value = true;
    });
  };

  // 初始化菜单树选项
  const initMenuTreeOptions = async () => {
    try {
      const result = await getMenuList();
      if (result) {
        // 添加根节点
        const rootMenu: MenuInfo = {
          id: 0,
          title: '主类目',
          children: handleMenuTree(result)
        };
        menuTreeOptions.value = [rootMenu];
      }
    } catch (error) {
      console.error('Load menu tree options error:', error);
    }
  };

  // 新增菜单
  const handleAdd = () => {
    dialogTitle.value = '新增菜单';
    isEdit.value = false;
    formData.value = {
      title: '',
      name: '',
      path: '',
      component: '',
      icon: '',
      sort: 0,
      menuType: 'M',
      visible: '1',
      status: '0',
      roles: '',
      parentId: 0,
    };
    initMenuTreeOptions();
    dialogVisible.value = true;
  };

  // 新增子菜单
  const handleAddChild = (record: MenuInfo) => {
    dialogTitle.value = '新增子菜单';
    isEdit.value = false;
    formData.value = {
      title: '',
      name: '',
      path: '',
      component: '',
      icon: '',
      sort: 0,
      menuType: 'C',
      visible: '1',
      status: '0',
      roles: '',
      parentId: record.id!,
    };
    initMenuTreeOptions();
    dialogVisible.value = true;
  };

  // 编辑菜单
  const handleEdit = (record: MenuInfo) => {
    console.log(record);
    dialogTitle.value = '编辑菜单';
    isEdit.value = true;
    formData.value = { ...record };
    // formData.value.icon = icon.value;
    initMenuTreeOptions();
    dialogVisible.value = true;
  };

  // 提交表单
  const handleSubmit = async () => {
    try {
      const valid = await formRef.value?.validate();
      if (!valid) return;

      submitLoading.value = true;

      if (isEdit.value) {
        await updateMenu(formData.value);
        ElMessage.success('更新菜单成功');
      } else {
        await saveMenu(formData.value);
        ElMessage.success('新增菜单成功');
      }

      dialogVisible.value = false;
      loadMenuList();
    } catch (error) {
      ElMessage.error(isEdit.value ? '更新菜单失败' : '新增菜单失败');
      console.error('Submit menu error:', error);
    } finally {
      submitLoading.value = false;
    }
  };

  // 删除菜单
  const handleDelete = async (record: MenuInfo) => {
    try {
      await ElMessageBox.confirm('确定要删除该菜单吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      });

      await deleteMenus(record.id!);
      ElMessage.success('删除菜单成功');
      loadMenuList();
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('删除菜单失败');
        console.error('Delete menu error:', error);
      }
    }
  };
</script>

<style scoped lang="scss">
  .menu-container {
    padding: 24px;
    background-color: #fff;
    border-radius: 8px;
    margin: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .menu-header {
      margin-bottom: 24px;
      padding-bottom: 12px;
      border-bottom: 2px solid #f0f2f5;

      h3 {
        margin: 0;
        font-size: 20px;
        font-weight: 600;
        color: #1f2937;
      }
    }

    .search-section {
      margin-bottom: 20px;
      // padding: 20px;
      // background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
      border-radius: 8px;
      // border: 1px solid #e2e8f0;
    }

    .action-section {
      margin-bottom: 20px;
      display: flex;
      gap: 12px;
      justify-content: right;
    }

    .table-section {
      background: #fff;
      border-radius: 8px;
      overflow: hidden;
    }
  }

  :deep(.el-dialog__body) {
    padding: 20px;
  }
</style>
