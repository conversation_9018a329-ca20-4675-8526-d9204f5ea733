import { FormOptions } from '/@/components/sys/BasicForm/types';
import { TableOptions } from '/@/components/sys/BasicTable/types';

// 表格列配置
export const columns: TableOptions[] = [
  {
    type: 'selection',
    width: 50,
    label: '复选',
    align: 'center',
  },
  {
    type: 'index',
    width: 60,
    label: '序号',
    align: 'center',
  },
  {
    label: '角色名称',
    prop: 'roleName',
    width: 150,
    align: 'center',
  },
  {
    label: '权限字符',
    prop: 'roleKey',
    width: 150,
    align: 'center',
  },
  {
    label: '显示顺序',
    prop: 'roleSort',
    width: 100,
    align: 'center',
  },
  {
    label: '状态',
    prop: 'status',
    width: 80,
    align: 'center',
    slot: 'status',
  },
  {
    label: '创建时间',
    prop: 'createTime',
    width: 160,
    align: 'center',
  },
  {
    label: '备注',
    prop: 'remark',
    minWidth: 200,
    align: 'center',
  },
  {
    label: '操作',
    prop: 'action',
    action: true,
    fixed: 'right',
    width: 200,
    align: 'center',
  },
];

// 搜索表单配置
export const searchFormSchema: FormOptions[] = [
  {
    field: 'roleName',
    label: '角色名称',
    component: 'Input',
    componentProps: {
      placeholder: '请输入角色名称',
    },
    span: 6,
  },
  {
    field: 'roleKey',
    label: '权限字符',
    component: 'Input',
    componentProps: {
      placeholder: '请输入权限字符',
    },
    span: 6,
  },
  {
    field: 'status',
    label: '状态',
    component: 'Select',
    componentProps: {
      placeholder: '请选择状态',
      options: [
        { label: '全部', value: '' },
        { label: '正常', value: '0' },
        { label: '停用', value: '1' },
      ],
    },
    span: 6,
  },
];

// 角色表单配置
export const roleFormSchema: FormOptions[] = [
  {
    field: 'id',
    label: 'ID',
    component: 'Input',
    show: false,
  },
  {
    field: 'roleName',
    label: '角色名称',
    component: 'Input',
    required: true,
    componentProps: {
      placeholder: '请输入角色名称',
    },
    span: 12,
  },
  {
    field: 'roleKey',
    label: '权限字符',
    component: 'Input',
    required: true,
    componentProps: {
      placeholder: '请输入权限字符',
    },
    span: 12,
  },
  {
    field: 'roleSort',
    label: '显示顺序',
    component: 'InputNumber',
    required: true,
    componentProps: {
      placeholder: '请输入显示顺序',
      min: 0,
    },
    span: 12,
  },
  {
    field: 'status',
    label: '状态',
    component: 'Select',
    required: true,
    componentProps: {
      placeholder: '请选择状态',
      options: [
        { label: '正常', value: '0' },
        { label: '停用', value: '1' },
      ],
    },
    span: 12,
  },
  {
    field: 'remark',
    label: '备注',
    component: 'InputTextArea',
    componentProps: {
      placeholder: '请输入备注',
      rows: 4,
    },
    span: 24,
  },
];
