<template>
  <div class="team-container">
    <div class="main">
      <!-- <h2 class="sidebar-title"
        ><span>团队信息</span>
        <el-button type="primary" @click="handleAdd"> 新增 </el-button></h2
      > -->
      <div class="sider" v-if="isData">
        <h2 class="sidebar-title">团队信息</h2>
        <div class="divider"></div>
        <div class="sider-center">
          <div
            v-for="(item, index) in menuList"
            :key="item.id"
            class="list"
            @click="selectItem(index)"
            :class="{ active: selectedIndex === index }"
          >
            <div class="list-title">
              <!-- <span
                class="annotation"
                :style="{ background: item.environment.color.toLocaleLowerCase() }"
              >
              </span> -->
              <!-- <el-icon class="annotation"><Aim /></el-icon> -->
              <span>{{ item.name }}</span>

              <span v-if="item.local"> （当前团队） </span>
            </div>

            <div>
              <!-- <el-icon style="margin-right: 8px; color: #409eff" size="12"
                ><DocumentCopy
              /></el-icon> -->
              <el-icon
                @click.stop
                style="margin-right: 8px; color: #409eff"
                size="12"
                v-if="!item.local"
                @click="handleDelete(item)"
                ><Delete
              /></el-icon>
              <!-- <el-popconfirm
                :icon="InfoFilled"
                title="确定要删除吗?"
                @confirm="handleDeketeDatasource(item.id)"
              >
                <template #reference>
                  <el-icon
                    @click.stop
                    style="margin-right: 8px; color: #409eff"
                    size="12"
                    ><Delete
                  /></el-icon>
                </template>
              </el-popconfirm> -->
            </div>
          </div>
        </div>
        <div class="sider-bottom">
          <el-button :icon="Plus" @click="handleAdd">添加团队</el-button>
        </div>
      </div>
      <div class="content">
        <ul v-if="isData">
          <li>
            <div class="li-info">
              <div>
                <span class="label"> 团队头像 </span>
                <div class="assistant-icon">
                  {{ teamInfo.name && getFirstChar(teamInfo.name) }}
                </div>
              </div>
            </div>
            <div class="li-divider"></div>
          </li>

          <li>
            <div class="li-info">
              <div>
                <span class="label"> 团队名称 </span>
                <span v-if="!nameEdit"> {{ teamInfo.name }} </span>
                <el-input v-else v-model="teamInfo.name" placeholder="请输入团队名称" />
              </div>
              <el-button v-if="!nameEdit" @click="editName"> 编辑 </el-button>
              <el-button v-else @click="saveName"> 保存 </el-button>
            </div>
            <div class="li-divider"></div>
          </li>

          <!-- <li>
            <div class="li-info">
              <div>
                <span class="label"> 团队号 </span>
                <span> {{ formData.num }} </span>
              </div>
            </div>
            <div class="li-divider"></div>
          </li> -->

          <li>
            <div class="li-last">
              <div>
                <span class="label"> 团队描述 </span>
                <!-- <span> {{ formData.description }} </span> -->

                <span v-if="!desEdit"> {{ teamInfo.description }} </span>

                <el-input
                  v-model="teamInfo.description"
                  type="textarea"
                  :rows="4"
                  v-else
                  placeholder="请输入团队描述"
                />
              </div>
              <el-button v-if="!desEdit" @click="editDes"> 编辑 </el-button>
              <el-button v-else @click="saveDes"> 保存 </el-button>
            </div>
          </li>
          <!-- <li>
            <div>
              <span> 团队号 </span>
              <span> SASD55566SDSFD </span>
            </div>
            <div class="divider"></div>
          </li>

          <li>
            <div>
              <span> 团队描述 </span>
              <span> SASD55566SDSFD </span>
            </div>
          </li> -->
        </ul>
        <el-empty v-else description="暂未添加团队">
          <el-button type="primary" @click="handleAdd">添加团队</el-button>
        </el-empty>
      </div>
    </div>

    <el-dialog
      v-model="visible"
      title="添加团队"
      width="500px"
      :destroy-on-close="true"
      :close-on-click-modal="false"
    >
      <basic-form
        :formList="formSchema"
        :isCreate="false"
        :formData="formData"
        :showSubmit="false"
        ref="formRef"
        label-width="80"
      />
      <template #footer>
        <el-button @click="visible = false">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="loading"
          >保存</el-button
        >
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
  import { ref, markRaw } from 'vue';
  // teamAwitchApi,
  import { temamEditApi, temamAddApi, deleteTeamApi } from '../../api/index';
  import { getAuthStorage } from '/@/utils/storage/auth';

  import { formSchema } from './data';
  import { ElMessage, ElMessageBox } from 'element-plus';
  import { useUserStore } from '/@/stores/modules/user';
  import { Plus, Delete, CircleClose } from '@element-plus/icons-vue';
  // import { cloneDeep } from 'lodash-es';
  const userInfo: any = getAuthStorage();
  const userStore = useUserStore();
  console.log(userInfo.teamInfo);
  const formRef = ref<HTMLDivElement | null>(null);
  const formData = ref<any>({
    createByName: null,
    createTime: '',
    description: '',
    id: '',
    memberCount: null,
    members: null,
    name: '',
    status: 1,
    statusDesc: '',
    updateByName: null,
    updateTime: '',
  });

  const teamInfo = ref<any>({});
  const isData = ref(false);
  const visible = ref(false);
  const loading = ref(false);
  const selectedIndex = ref(0);
  const menuList = ref<any[]>([
    // {
    //   label: '智能体编排',
    //   id: 1,
    // },
    // {
    //   label: '智能体配置',
    //   id: 2,
    // },
    // {
    //   label: '工具管理',
    //   id: 3,
    // },
    // {
    //   label: '全局配置',
    //   id: 4,
    // },
  ]);

  const init = (data) => {
    selectedIndex.value = 0;
    menuList.value = data.allTeams;
    menuList.value = data.allTeams.map((item) => {
      const local = item.id === data.currentTeamId;
      return {
        ...item,
        local,
      };
    });
    teamInfo.value = menuList.value[selectedIndex.value];
    if (data.currentTeamId) {
      isData.value = true;
    } else {
      isData.value = false;
    }
  };
  init(userInfo.teamInfo);
  // const selectedIndex = ref(0); //   0
  const selectItem = (index) => {
    selectedIndex.value = index; // 更新选中的索引
    teamInfo.value = menuList.value[index];
    // currentComponent.value = menuList.value[index].value;
  };

  const nameEdit = ref(false);
  const desEdit = ref(false);

  const getFirstChar = (title: string): string => {
    return title.charAt(0).toUpperCase();
  };

  const editName = () => {
    nameEdit.value = true;
  };
  const saveName = async () => {
    const res = await temamEditApi(teamInfo.value);
    if (res) {
      nameEdit.value = false;
      // getTeamInfo();
      await userStore.reFreshUser();
      ElMessage.success('更新成功');
    }
  };
  const editDes = () => {
    desEdit.value = true;
  };
  const saveDes = async () => {
    const res = await temamEditApi(teamInfo.value);
    if (res) {
      desEdit.value = false;
      // getTeamInfo();

      await userStore.reFreshUser();
      ElMessage.success('更新成功');
    }
  };

  const handleAdd = () => {
    visible.value = true;
    formData.value = {
      createByName: null,
      createTime: '',
      description: '',
      id: '',
      memberCount: null,
      members: null,
      name: '',
      status: 1,
      statusDesc: '',
      updateByName: null,
      updateTime: '',
    };
  };
  const handleSave = () => {
    const getData = formRef.value && formRef.value?.submitForm;
    const ruleFormRef = formRef.value && formRef.value?.ruleFormRef;
    getData(ruleFormRef, (status, data) => {
      if (status === 'success') {
        loading.value = true;

        temamAddApi({ ...data, status: 1 })
          .then(async (res) => {
            ElMessage({
              message: '新增成功',
              type: 'success',
            });

            teamInfo.value = res;
            // teamAwitchApi(teamInfo.value.id).then(async () => {
            //   await userStore.reFreshUser();
            //   init();
            // });
            const info = await userStore.reFreshUser();
            // console.log('info');

            // console.log(info.teamInfo);
            init(info.teamInfo);
            // setTimeout((),100)

            // setTimeout(() => {
            //   init();
            // }, 100);
            // await userStore.reFreshUser();

            // console.log('info');
            //
            loading.value = false;
            visible.value = false;

            // getTeamInfo();
          })
          .catch(() => {
            loading.value = false;
          });

        // UpdatePassword({
        //   id: userInfo.id,
        //   userName: userInfo.userName,
        //   userPasswd: encryption.pwdEncryptByAES(data.userPasswd),
        //   newPasswd: encryption.pwdEncryptByAES(data.newPasswd),
        // }).then(() => {
        //   ElMessage({
        //     message: '修改密码成功',
        //     type: 'success',
        //   });
        //   logout();
        // });
      }
    });
  };

  const handleDelete = async (record) => {
    // await ElMessageBox.confirm('确定要删除该角色吗？', '提示', {
    //   confirmButtonText: '确定',
    //   cancelButtonText: '取消',
    //   type: 'warning',
    // });

    // // await deleteRoles(record.id!);
    // ElMessage.success('删除角色成功');
    // // loadRoleList();

    ElMessageBox.confirm('确定要删除所选团队吗？', '提示', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'error',
      icon: markRaw(CircleClose),
    })
      .then(() => {
        // let params = {};
        // if (item === null) {
        //   params = selectionIds.value;
        // } else {
        //   params = [item.id];
        // }
        deleteTeamApi(record.id).then(async () => {
          const info = await userStore.reFreshUser();
          init(info.teamInfo);
          ElMessage({
            showClose: true,
            type: 'success',
            message: '团队删除成功',
          });
          // if (type === 1) {
          //   selectionIds.value = [];
          //   tableRef.value!.clearSelection();
          // }
          // getRoleList();
        });
      })
      .catch(() => {});
  };
</script>

<style scoped lang="scss">
  .team-container {
    height: calc(100vh - 62px);
    border: 1px solid #e6e8ee;
    background-color: #fff;
    border-radius: 8px;
    margin: 0 8px 8px 0;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    .main {
      //   display: flex;
      //   flex: 1;
      display: flex;
      flex: 1;

      .sider {
        flex: 0 0 320px;
        background: #ffffff;
        border-right: 1px solid #e6e8ee;
        display: flex;
        flex-direction: column;
        height: calc(100vh - 65px);
        .sider-title {
          line-height: 36px;
          margin-bottom: 12px;
          font-size: 16px;
          height: 36px;
        }
        .sider-center {
          // flex: 1;
          overflow-y: auto;
          height: calc(100vh - 180px);
          .list.active {
            color: #409eff;
            background-color: #ecf5ff;
            border-right: 3px solid #409eff;
          }
          .list {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            // margin: 0 12px;
            // border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s;
            font-size: 14px;
            color: #606266;
            display: flex;
            justify-content: space-between;
            .el-icon {
              font-size: 16px;
              margin-top: 3px;
            }
            &:hover {
              background-color: #f5f7fa;
            }
            .list-title {
              white-space: nowrap; /* 防止文本换行 */
              overflow: hidden; /* 隐藏溢出的内容 */
              text-overflow: ellipsis; /* 显示省略符号来代表被修剪的文本 */
              width: 200px;
            }
          }
        }
        .sider-bottom {
          height: 30px;
          padding: 0 12px;
          .el-button {
            width: 100%;
          }
        }
        .sidebar-title {
          font-size: 18px;
          font-weight: bold;
          color: #303133;
          padding: 0 20px;
          margin: 15px 0 15px 0;
        }

        .divider {
          height: 1px;
          background-color: #ebeef5;
          margin-bottom: 15px;
        }
      }
      .content {
        flex: 1;
        overflow: auto;
        // background: #f6f8fa;
        ul {
          padding: 20px;
          //   border: 1px soild #ebeef5;
          border: 1px solid #ebeef5;
          margin: 20px;
          li {
            // margin-bottom: 12px;
            // div {
            //   margin-bottom: 12px;
            // }
            .li-info {
              margin-bottom: 20px;
              display: flex;
              justify-content: space-between;
              width: 100%;
              div {
                // width: 400px;
                width: 90%;
                display: flex;
                // justify-content: space-between;
                .label {
                  display: inline-block;
                  width: 100px;
                  margin-right: 80px;
                }

                .assistant-icon {
                  width: 48px;
                  height: 48px;
                  border-radius: 50%;
                  background: linear-gradient(135deg, #667eea 0%, #1a5efe 100%);
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  color: white;
                  font-size: 20px;
                  font-weight: 600;
                  flex-shrink: 0;
                }
              }
            }
            .li-last {
              display: flex;
              justify-content: space-between;
              width: 100%;
              div {
                // width: 400px;
                width: 90%;
                display: flex;
                // justify-content: space-between;

                .label {
                  display: inline-block;
                  width: 100px;
                  margin-right: 80px;
                }
              }
            }
          }
        }
      }

      .li-divider {
        height: 1px;
        background-color: #ebeef5;
        margin-bottom: 20px;
      }
    }
  }
</style>
