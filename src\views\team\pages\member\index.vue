<template>
  <div class="role-container">
    <div class="role-header">
      <h3>成员管理</h3>
    </div>

    <!-- 操作按钮区域 -->
    <div class="action-section">
      <el-button type="primary" @click="handleAdd" :icon="Plus">
        {{ tableData.length + '/' + allUserOptions.length }}添加成员
      </el-button>
      <!-- <el-button
        type="danger"
        @click="handleBatchDelete"
        :icon="Delete"
        :disabled="selectedIds.length === 0"
      >
        批量删除
      </el-button>
      <el-button @click="handleRefresh" :icon="Refresh"> 刷新 </el-button> -->
    </div>

    <!-- 表格区域 -->
    <div class="table-section">
      <el-table
        ref="tableRef"
        :data="tableData"
        v-loading="loading"
        style="width: 100%"
        stripe
        border
        height="calc(100vh - 260px)"
      >
        <el-table-column type="index" label="序号" width="60" align="center" />
        <el-table-column prop="userName" label="名称" show-overflow-tooltip />
        <el-table-column prop="roleName" label="角色" show-overflow-tooltip />
        <el-table-column prop="joinTime" label="修改时间" show-overflow-tooltip />
        <el-table-column label="操作" fixed="right" width="260" align="center">
          <template #default="{ row }">
            <!-- <el-button type="primary" link size="small" @click="handleAssignUsers(row)">
              分配用户
            </el-button>
            <el-button type="primary" link size="small" @click="handlePermission(row)">
              数据权限
            </el-button> -->
            <el-button
              type="primary"
              v-if="row.roleId === '3'"
              link
              size="small"
              @click="handleRole(row)"
            >
              设为团队成员
            </el-button>
            <el-button type="primary" v-else link size="small" @click="handleRole(row)">
              设为团队管理员
            </el-button>

            <el-button type="danger" link size="small" @click="handleDelete(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <!-- <div class="pagination-section">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div> -->
    </div>

    <el-dialog
      v-model="userDialogVisible"
      title="分配用户"
      width="700px"
      :close-on-click-modal="false"
      destroy-on-close
    >
      <el-transfer
        v-model="selectedUserIds"
        :data="userOptions"
        :titles="['未授权用户', '已授权用户']"
        :button-texts="['取消', '添加']"
        :props="{ key: 'id', label: 'name' }"
        filterable
        filter-placeholder="请输入姓名"
        style="text-align: left; display: inline-block"
      />
      <template #footer>
        <el-button @click="userDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmitUsers" :loading="submitLoading">
          确定
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted } from 'vue';
  import { ElMessage, ElMessageBox } from 'element-plus';
  import { Plus } from '@element-plus/icons-vue';

  import {
    getTeamInfoApi,
    getTeamMemberApi,
    teamAddUserApi,
    teamRemoveMemberApi,
    teamUpdateMemberApi,
  } from '../../api/index';
  import { getUserPageList } from '/@/api/sys/users';
  const tableRef = ref();

  const tableData = ref<any[]>([]);
  const loading = ref(false);
  const submitLoading = ref(false);

  // 表单数据

  const userDialogVisible = ref(false);
  const selectedUserIds = ref<string[]>([]);
  const userOptions = ref<any[]>([]);
  const allUserOptions = ref<any[]>([]);

  const teamId = ref('');
  const getTeamInfo = async () => {
    const res = await getTeamInfoApi();
    console.log(res);
    // isData.value = false;
    // if (res.length > 0) {
    //   teamId.value = res[0].id;
    //   getTeamMember(teamId.value);
    // } else {
    //   teamId.value = '';
    // }
    if (!!res.currentTeam.id) {
      getTeamMember(res.currentTeam.id);
    }
  };

  const getTeamMember = (teamId) => {
    getTeamMemberApi(teamId).then((res) => {
      console.log(res);
      tableData.value = res;

      userOptions.value = keepUniqueFromFirst(allUserOptions.value, tableData.value);
    });
  };

  const getUsers = () => {
    const params = {
      currentPage: 1,
      pageSize: 10000,
    };
    getUserPageList(params).then((res) => {
      console.log(res);
      allUserOptions.value = res?.data;
      getTeamInfo();
      //   getTeamMember()
    });
  };
  function keepUniqueFromFirst(arr1, arr2) {
    return arr1.filter((item1) => !arr2.some((item2) => item2.userId === item1.id));
  }

  //   const params = {
  //     teamId: 'xxxx',
  //     roleId: 'xxxx',
  //     userId: ['xx','xxx'],
  //   }
  // const handleRadioClick = (val, index) => {
  //   console.log(dataPermissionList.value[index]);
  //   console.log(val);

  //   if (dataPermissionList.value[index].permissionValue === val.id) {
  //     // dataPermissionList.value[index].selectedValue = null;
  //     dataPermissionList.value[index].permissionValue = null;
  //     console.log(dataPermissionList.value[index]);
  //   } else {
  //     dataPermissionList.value[index].permissionValue = val.id;
  //   }
  // }

  // 生命周期
  onMounted(() => {
    //   loadRoleList();
    //   getDatasource();
    // getTeamInfo();
    getUsers();
    // getAllPermission()
  });

  // 新增角色
  const handleAdd = async () => {
    userDialogVisible.value = true;
  };

  // 删除角色
  const handleDelete = async (record) => {
    await ElMessageBox.confirm('确定要删除该角色吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    });

    const params = {
      teamId: teamId.value,
      userId: record.userId,
    };

    teamRemoveMemberApi(params).then(() => {
      ElMessage.success('删除角色成功');
      getTeamMember(teamId.value);
    });
  };

  // 提交用户分配
  const handleSubmitUsers = () => {
    const params = {
      teamId: teamId.value,
      roleId: '1967846444659613698',
      userIds: selectedUserIds.value,
    };
    console.log(params);
    teamAddUserApi(params).then(() => {
      userDialogVisible.value = false;
      getTeamMember(teamId.value);
      ElMessage.success('添加成功');
    });
  };

  const handleRole = (record) => {
    console.log(record);
    const params = {
      teamId: teamId.value,
      roleId: '',
      userId: record.userId,
    };
    if (record.roleId !== '3') {
      params.roleId = '3';
    } else {
      params.roleId = '1967846444659613698';
    }
    //    pageAuditLogs  审计  pageByParams 操作
    teamUpdateMemberApi(params).then(() => {
      userDialogVisible.value = false;
      getTeamMember(teamId.value);
      ElMessage.success('设置成功');
    });
  };
</script>

<style scoped lang="scss">
  .role-container {
    padding: 24px;
    background-color: #fff;
    border-radius: 8px;
    margin: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .role-header {
      margin-bottom: 24px;
      padding-bottom: 12px;
      border-bottom: 2px solid #f0f2f5;

      h3 {
        margin: 0;
        font-size: 20px;
        font-weight: 600;
        color: #1f2937;
      }
    }

    .search-section {
      margin-bottom: 20px;
      // padding: 20px;
      // background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
      // border-radius: 8px;
      // border: 1px solid #e2e8f0;
    }

    .action-section {
      margin-bottom: 20px;
      display: flex;
      gap: 12px;
      // align-items: center;
      justify-content: right;
    }

    .table-section {
      background: #fff;
      border-radius: 8px;
      overflow: hidden;

      .pagination-section {
        margin-top: 20px;
        padding: 16px 0;
        display: flex;
        justify-content: flex-end;
        // background: #fafbfc;
        // border-top: 1px solid #e2e8f0;
      }
    }
  }

  :deep(.el-dialog__body) {
    padding: 20px;
  }
</style>
