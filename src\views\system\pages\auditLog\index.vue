<template>
  <div class="role-container">
    <div class="role-header">
      <h3>审计日志</h3>
    </div>

    <!-- 搜索区域 -->
    <div class="search-section">
      <basic-search
        :searchArray="searchArray"
        class="search-all"
        ref="searchRef"
        :labelShow="false"
        @onSearch="logList"
        @reset="logList"
      />
      <!-- <el-form :inline="true" :model="searchParams" class="demo-form-inline">
        <el-form-item label="">
          <el-input
            v-model="searchParams.name"
            placeholder="请输入角色名称"
            clearable
          />
        </el-form-item>
        <el-form-item label="">
          <el-input
            v-model="searchParams.description"
            placeholder="请输入角色描述"
            clearable
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form> -->
    </div>

    <!-- 操作按钮区域 -->
    <div class="action-section">
      <!-- <el-button type="primary" @click="handleAdd" :icon="Plus"> 新增角色 </el-button> -->
      <el-button @click="handleRefresh" :icon="Refresh"> 刷新 </el-button>
    </div>

    <!-- 表格区域 -->
    <div class="table-section">
      <basic-table
        :columns="columns"
        :data="tableData"
        :total="pagination.total"
        :page-size="pagination.pageSize"
        :current-page="pagination.current"
        @page-change="handlePageChange"
        @size-change="handleSizeChange"
        :downSetting="true"
        :setting="false"
        height="calc(100vh - 418px)"
      >
        <template #businessType="{ record }">
          <el-tag v-if="record.businessType === 1" type="success">新增</el-tag>
          <el-tag v-if="record.businessType === 2">修改</el-tag>
          <el-tag v-if="record.businessType === 3" type="danger">删除</el-tag>
          <el-tag v-if="record.businessType === 0" type="warning">其他</el-tag>
        </template>
        <template #requestMethod="{ record }">
          <el-tag v-if="!!record.requestMethod" :type="tagType(record.requestMethod)">{{
            record.requestMethod
          }}</el-tag>
        </template>
        <!--  -->
        <template #action="scope">
          <el-button type="primary" link @click="viewClick(scope.record)"
            >查看</el-button
          >
        </template>
      </basic-table>
      <!-- <el-table
        ref="tableRef"
        :data="tableData"
        v-loading="loading"
        style="width: 100%"
        stripe
        border
        height="calc(100vh - 410px)"
      >
        <el-table-column type="index" label="序号" width="60" align="center" />
        <el-table-column prop="name" label="角色名称" show-overflow-tooltip />
        <el-table-column prop="description" label="角色描述" show-overflow-tooltip />
        <el-table-column
          prop="sort"
          width="90"
          label="显示顺序"
          show-overflow-tooltip
          align="center"
        />
        <el-table-column label="操作" fixed="right" width="200" align="center">
          <template #default="{ row }">
            <el-button type="primary" link size="small"> 编辑 </el-button>
          </template>
        </el-table-column>
      </el-table> -->

      <!-- 分页 -->
      <!-- <div class="pagination-section">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div> -->
    </div>

    <el-drawer v-model="visible" title="日志详情" size="800">
      <view-log class="view-log" :record="record1" />
      <template #footer>
        <el-button @click="visible = false">取消</el-button>
      </template>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted, computed } from 'vue';
  //   import { ElMessage } from 'element-plus';
  import { Refresh } from '@element-plus/icons-vue';
  //   import {
  //     getRolePageList,
  //     saveRole,
  //     updateRole,
  //     deleteRoles,
  //     getRoleDetail,
  //     getAuthRoleDetail,
  //     saveAuthRole,
  //     type RoleInfo,
  //     type RoleQueryParams
  //   } from '/@/api/sys/roles';
  import { getAuditLogsApi } from '../../api/index';
  //   import { getMenuList } from '/@/api/sys/menus';
  import ViewLog from './ViewLog.vue';
  import dayjs from 'dayjs';
  import { searchArray, columns } from './log.data';
  const record1 = ref({});
  // 响应式数据
  const tableData = ref<any[]>([]);
  //   const loading = ref(false);
  const visible = ref(false);
  // 分页数据
  const pagination = ref({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const searchRef = ref({
    userName: '',
    businessType: '',
    time: '',
  });
  // 搜索参数
  //   const searchParams = ref<any>({});

  // 生命周期
  onMounted(() => {
    // loadRoleList();
    logList();
  });

  //   const getList = () => {
  //     const params = {
  //       currentPage: pagination.value.current,
  //       pageSize: pagination.value.pageSize,
  //       requestMethod: '',
  //     };
  //     getAuditLogsApi(params).then((res) => {
  //       console.log('getAuditLogsApi');
  //       console.log(res);
  //     });
  //   };
  function logList() {
    const { current, pageSize } = pagination.value;
    const { userName, time, businessType, requestMethod } =
      searchRef?.value?.['searchValue'] || {};
    const params: any = {
      currentPage: current,
      pageSize,
      userName,
      businessType,
      requestMethod,
      operatorType: 2,
    };
    if (time) {
      params.startTime = dayjs(time[0]).format('YYYY-MM-DD') + ' ' + '00:00:00';
      params.endTime = dayjs(time[1]).format('YYYY-MM-DD') + ' ' + '23:59:59';
    }
    console.log(params);
    getAuditLogsApi(params).then((res) => {
      // const { data, total } = res;
      tableData.value = res.data;
      pagination.value = {
        total: Number(res.total),
        current: res.currentPage,
        pageSize: res.pageSize,
      };

      //         const pagination = ref({
      //     current: 1,
      //     pageSize: 10,
      //     total: 0,
      //   });
    });
  }
  // logList();

  const tagType = computed(() => (item: any) => {
    //计算属性传递参数
    if (item === 'GET') {
      return 'success';
    }
    if (item === 'UPDATE') {
      return '';
    }
    if (item === 'PUT') {
      return '';
    }
    if (item === 'POST') {
      return 'warning';
    }
    if (item === 'DELETE') {
      return 'danger';
    }
    if (item === 'INSERT') {
      return 'warning';
    }
  });

  const viewClick = (row) => {
    console.log('record', record1);
    visible.value = true;
    record1.value = row;
  };
  // 加载角色列表
  //   const loadRoleList = async () => {
  //     try {
  //       loading.value = true;
  //       const params = {
  //         ...searchParams.value,
  //         currentPage: pagination.value.current,
  //         pageSize: pagination.value.pageSize,
  //         roleType: 'menu',
  //       };

  //       const result = await getRolePageList(params);
  //       if (result && result.data) {
  //         tableData.value = result.data;
  //         pagination.value.total = result.total;
  //       }
  //     } catch (error) {
  //       ElMessage.error('加载角色列表失败');
  //       console.error('Load role list error:', error);
  //     } finally {
  //       loading.value = false;
  //     }
  //   };

  //   // 搜索处理
  //   const handleSearch = () => {
  //     pagination.value.current = 1;
  //     // loadRoleList();
  //   };

  //   // 重置搜索
  //   const handleReset = () => {
  //     searchParams.value = {};
  //     pagination.value.current = 1;
  //     loadRoleList();
  //   };

  // 刷新
  const handleRefresh = () => {
    logList();
  };

  // 分页处理
  const handlePageChange = (page: number) => {
    pagination.value.current = page;
    logList();
  };

  const handleSizeChange = (size: number) => {
    pagination.value.pageSize = size;
    pagination.value.current = 1;
    logList();
  };
</script>

<style scoped lang="scss">
  .role-container {
    padding: 24px;
    background-color: #fff;
    border-radius: 8px;
    margin: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .role-header {
      margin-bottom: 24px;
      padding-bottom: 12px;
      border-bottom: 2px solid #f0f2f5;

      h3 {
        margin: 0;
        font-size: 20px;
        font-weight: 600;
        color: #1f2937;
      }
    }

    .search-section {
      margin-bottom: 20px;
      // padding: 20px;
      // background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
      // border-radius: 8px;
      // border: 1px solid #e2e8f0;
    }

    .action-section {
      margin-bottom: 20px;
      display: flex;
      gap: 12px;
      // align-items: center;
      justify-content: right;
    }

    .table-section {
      background: #fff;
      border-radius: 8px;
      overflow: hidden;

      .pagination-section {
        margin-top: 20px;
        padding: 16px 0;
        display: flex;
        justify-content: flex-end;
        // background: #fafbfc;
        // border-top: 1px solid #e2e8f0;
      }
    }
  }

  :deep(.el-dialog__body) {
    padding: 20px;
  }
</style>
